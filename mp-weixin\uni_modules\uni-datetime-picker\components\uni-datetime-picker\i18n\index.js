"use strict";
const en = {
  "uni-datetime-picker.selectDate": "select date",
  "uni-datetime-picker.selectTime": "select time",
  "uni-datetime-picker.selectDateTime": "select date and time",
  "uni-datetime-picker.startDate": "start date",
  "uni-datetime-picker.endDate": "end date",
  "uni-datetime-picker.startTime": "start time",
  "uni-datetime-picker.endTime": "end time",
  "uni-datetime-picker.ok": "ok",
  "uni-datetime-picker.clear": "clear",
  "uni-datetime-picker.cancel": "cancel",
  "uni-datetime-picker.year": "-",
  "uni-datetime-picker.month": "",
  "uni-calender.MON": "MON",
  "uni-calender.TUE": "TUE",
  "uni-calender.WED": "WED",
  "uni-calender.THU": "THU",
  "uni-calender.FRI": "FRI",
  "uni-calender.SAT": "SAT",
  "uni-calender.SUN": "SUN",
  "uni-calender.confirm": "confirm"
};
const zhHans = {
  "uni-datetime-picker.selectDate": "选择日期",
  "uni-datetime-picker.selectTime": "选择时间",
  "uni-datetime-picker.selectDateTime": "选择日期时间",
  "uni-datetime-picker.startDate": "开始日期",
  "uni-datetime-picker.endDate": "结束日期",
  "uni-datetime-picker.startTime": "开始时间",
  "uni-datetime-picker.endTime": "结束时间",
  "uni-datetime-picker.ok": "确定",
  "uni-datetime-picker.clear": "清除",
  "uni-datetime-picker.cancel": "取消",
  "uni-datetime-picker.year": "年",
  "uni-datetime-picker.month": "月",
  "uni-calender.SUN": "日",
  "uni-calender.MON": "一",
  "uni-calender.TUE": "二",
  "uni-calender.WED": "三",
  "uni-calender.THU": "四",
  "uni-calender.FRI": "五",
  "uni-calender.SAT": "六",
  "uni-calender.confirm": "确认"
};
const zhHant = {
  "uni-datetime-picker.selectDate": "選擇日期",
  "uni-datetime-picker.selectTime": "選擇時間",
  "uni-datetime-picker.selectDateTime": "選擇日期時間",
  "uni-datetime-picker.startDate": "開始日期",
  "uni-datetime-picker.endDate": "結束日期",
  "uni-datetime-picker.startTime": "開始时间",
  "uni-datetime-picker.endTime": "結束时间",
  "uni-datetime-picker.ok": "確定",
  "uni-datetime-picker.clear": "清除",
  "uni-datetime-picker.cancel": "取消",
  "uni-datetime-picker.year": "年",
  "uni-datetime-picker.month": "月",
  "uni-calender.SUN": "日",
  "uni-calender.MON": "一",
  "uni-calender.TUE": "二",
  "uni-calender.WED": "三",
  "uni-calender.THU": "四",
  "uni-calender.FRI": "五",
  "uni-calender.SAT": "六",
  "uni-calender.confirm": "確認"
};
const i18nMessages = {
  en,
  "zh-Hans": zhHans,
  "zh-Hant": zhHant
};
exports.i18nMessages = i18nMessages;
