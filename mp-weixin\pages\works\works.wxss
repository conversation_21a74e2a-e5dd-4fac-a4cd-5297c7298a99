/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 卡片视图 */
.gui-card-view.data-v-2475a018 {
  padding: 25rpx;
  margin: 0 30rpx;
  margin-top: 30rpx;
}
.gui-card-body.data-v-2475a018 {
  padding-bottom: 30rpx;
}
.gui-card-img.data-v-2475a018 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 80rpx;
}
.gui-card-desc.data-v-2475a018 {
  width: 400rpx;
  margin-left: 25rpx;
  flex: 1;
}
.gui-card-name.data-v-2475a018 {
  font-size: 28rpx;
  line-height: 40rpx;
}
.gui-card-update.data-v-2475a018 {
  width: 180rpx;
}
.gui-card-text.data-v-2475a018 {
  line-height: 40rpx;
  font-size: 24rpx;
}
.gui-card-footer.data-v-2475a018 {
  margin-top: 25rpx;
}
.gui-card-footer-item.data-v-2475a018 {
  text-align: center;
  flex: 1;
  line-height: 38rpx;
  font-size: 26rpx;
}
.gui-card-footer-item.data-v-2475a018 {
  display: block;
}
.gui-box-shadow.data-v-2475a018 {
  box-shadow: 0px 0px 3rpx #999999;
}