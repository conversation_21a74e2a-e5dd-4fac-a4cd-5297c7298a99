package com.atguigu.tingshu.common.minio.service.impl;

import com.atguigu.tingshu.common.minio.config.MinioAutoConfiguration;
import com.atguigu.tingshu.common.minio.config.MinioProperties;
import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.common.minio.service.FileUploadService;
import com.atguigu.tingshu.common.util.MD5;
import io.minio.*;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @version 1.0
 */
//@Service
@Slf4j
public class FileUploadServiceImpl implements FileUploadService {

    @Autowired
    private MinioProperties minioProperties;

    @Autowired
    private MinioAutoConfiguration minioAutoConfiguration;

    @Autowired
    private MinioClient minioClient;

    @Override
    public String fileUploadService(MultipartFile file) throws IOException, NoSuchAlgorithmException, InvalidKeyException {

        // TODO: 不同命但是内容是同一个做去重.(难度高)
        // 不同名 1.png  2.png

        String objectKey = "";

        //xxxbbb.png
        String originalFilename = file.getOriginalFilename();
        try {
            byte[] bytes = file.getBytes();

            String s = new String(bytes);


            String prefix = MD5.encrypt(s);
            String suffix = originalFilename.substring(originalFilename.lastIndexOf("."), originalFilename.length());

            objectKey = prefix + suffix;
            //1. 检查该上传的文件是否在minio中
            StatObjectArgs.Builder builder = StatObjectArgs.builder();
            StatObjectArgs statObjectArgs = builder
                    .bucket(minioProperties.getBucketName())
                    .object(objectKey)
                    .build();
            minioClient.statObject(statObjectArgs);
            //2. minio有 则直接返回
            return minioProperties.getEndpointUrl() + "/" + minioProperties.getBucketName() + "/" + objectKey;
        } catch (IOException e) {
            log.error("上传的文件不存在: {}", e.getMessage());
            throw new GuiguException(201, "上传的文件不存在");
        } catch (Exception e) {

            //3. minio没有, 可以直接上传
            log.info("该文件在桶中不存在, 可以上传到minio中");

            try {
                //4. 上传文件到桶中
                PutObjectArgs.Builder putObjectArgsBuilder = PutObjectArgs.builder();
                PutObjectArgs putObjectArgs = putObjectArgsBuilder
                        .bucket(minioProperties.getBucketName())
                        .object(objectKey)
                        .stream(file.getInputStream(), file.getSize(), -1)
                        .build();
                MinioClient minioClient = minioAutoConfiguration.minioClient(minioProperties);
                minioClient.putObject(putObjectArgs);
                log.info("上传文件到minio成功: {}", objectKey);
                //2. 获取照片的url
                //http://188.133.0.5:9000/tingshu/2YJmv5DdnWlN2426018ed35706edfc8c93c5a3bae8e6.png
                return minioProperties.getEndpointUrl() + "/" + minioProperties.getBucketName() + "/" + objectKey;
            } catch (MinioException es) {
                log.error("文件上传到minio失败: {}", es.getMessage());
                throw new GuiguException(201, "文件上传到minio失败");
            }
        }
    }


}
