/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup-dialog {
  width: 300px;
  border-radius: 11px;
  background-color: #fff;
}
.uni-dialog-title {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 25px;
}
.uni-dialog-title-text {
  font-size: 16px;
  font-weight: 500;
}
.uni-dialog-content {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 20px;
}
.uni-dialog-content-text {
  font-size: 14px;
  color: #6C6C6C;
}
.uni-dialog-button-group {
  display: flex;
  flex-direction: row;
  border-top-color: #f5f5f5;
  border-top-style: solid;
  border-top-width: 1px;
}
.uni-dialog-button {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 45px;
}
.uni-border-left {
  border-left-color: #f0f0f0;
  border-left-style: solid;
  border-left-width: 1px;
}
.uni-dialog-button-text {
  font-size: 16px;
  color: #333;
}
.uni-button-color {
  color: #007aff;
}
.uni-dialog-input {
  flex: 1;
  font-size: 14px;
  border: 1px #eee solid;
  height: 40px;
  padding: 0 10px;
  border-radius: 5px;
  color: #555;
}
.uni-popup__success {
  color: #4cd964;
}
.uni-popup__warn {
  color: #f0ad4e;
}
.uni-popup__error {
  color: #dd524d;
}
.uni-popup__info {
  color: #909399;
}