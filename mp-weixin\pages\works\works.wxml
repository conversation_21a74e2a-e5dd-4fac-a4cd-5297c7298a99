<gui-page wx:if="{{h}}" class="data-v-2475a018" u-s="{{['gHeader','gBody']}}" u-i="2475a018-0" bind:__l="__l" u-p="{{h}}"><view style="height:44px" class="gui-flex gui-nowrap gui-rows gui-align-items-center data-v-2475a018" slot="gHeader"><gui-header-leading class="data-v-2475a018" bindgoHome="{{a}}" u-i="2475a018-1,2475a018-0" bind:__l="__l"></gui-header-leading><text class="gui-h5 gui-bold gui-flex1 gui-text-center gui-ellipsis gui-primary-text data-v-2475a018">我的作品</text><view class="gui-flex data-v-2475a018"><text class="gui-icons gui-block gui-color-drak gui-p-10 gui-b-50 gui-bg-black-opacity3 gui-m-r-10 data-v-2475a018"></text></view><view class="data-v-2475a018" style="width:180rpx"></view></view><view slot="gBody"><gui-switch-navigation wx:if="{{b}}" class="data-v-2475a018" u-i="2475a018-2,2475a018-0" bind:__l="__l" u-p="{{b}}"></gui-switch-navigation><view class="gui-flex gui-p-l-30 data-v-2475a018"><gui-select-menu wx:if="{{e}}" class="r data-v-2475a018" bindselect="{{d}}" u-r="selectMenu3" u-i="2475a018-3,2475a018-0" bind:__l="__l" u-p="{{e}}"></gui-select-menu></view><scroll-view class="data-v-2475a018" scroll-y="{{true}}" style="{{'height:' + g}}"><view wx:for="{{f}}" wx:for-item="index" wx:for-index="i0" wx:key="a" class="gui-card-view gui-bg-white gui-dark-bg-level-3 gui-margin-top gui-box-shadow data-v-2475a018"><view class="gui-card-body gui-border-b gui-flex gui-rows gui-nowrap data-v-2475a018"><image mode="aspectFill" src="https://images.unsplash.com/photo-1663524789648-90fbdf8c8b76?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHwzMDF8fHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=100&q=90" class="gui-card-img data-v-2475a018"></image><view class="gui-card-desc data-v-2475a018"><view class="gui-flex gui-rows gui-nowrap gui-align-items-center data-v-2475a018"><text class="gui-card-name gui-primary-text gui-ellipsis data-v-2475a018">百年孤独 | 马克思传世名著(王明军演播)</text><text class="gui-card-update gui-text-small gui-primary-color data-v-2475a018">23天前更新</text></view><text class="gui-card-text gui-block gui-secondary-text gui-ellipsis data-v-2475a018" style="margin-top:10rpx"> 马尔克斯首部中文有声书，王明军演播马尔克斯首部中文有声书，王明军演播马尔克斯首部中文有声书，王明军演播 </text><view class="gui-flex gui-space-around gui-m-t-20 gui-color-gray gui-text-small data-v-2475a018"><text class="gui-icons gui-block gui-m-r-10 data-v-2475a018">  <text class="gui-m-l-10 data-v-2475a018">21</text></text><text class="gui-icons gui-block gui-m-r-10 data-v-2475a018">  <text class="gui-m-l-10 data-v-2475a018">14</text></text><text class="gui-icons gui-block gui-m-r-10 data-v-2475a018">  <text class="gui-m-l-10 data-v-2475a018">1</text></text></view></view></view><view class="gui-card-footer gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center data-v-2475a018"><text class="gui-icons gui-color-gray gui-card-footer-item gui-border-r data-v-2475a018">分享</text><text class="iconfont gui-color-gray gui-card-footer-item gui-border-r data-v-2475a018">数据</text><text class="gui-icons gui-color-gray gui-card-footer-item data-v-2475a018"> 编辑</text><text class="iconfont gui-color-gray gui-card-footer-item data-v-2475a018"> 更多</text></view></view></scroll-view></view></gui-page>