<view class="uni-data-pickerview"><scroll-view wx:if="{{a}}" class="selected-area" scroll-x="true"><view class="selected-list"><view wx:for="{{b}}" wx:for-item="item" wx:key="b" class="{{['selected-item', item.c && 'selected-item-active']}}" bindtap="{{item.d}}"><text>{{item.a}}</text></view></view></scroll-view><view class="tab-c"><scroll-view class="list" scroll-y="{{true}}"><view wx:for="{{c}}" wx:for-item="item" wx:key="d" class="{{['item', item.c && 'is-disabled']}}" bindtap="{{item.e}}"><text class="item-text">{{item.a}}</text><view wx:if="{{item.b}}" class="check"></view></view></scroll-view><view wx:if="{{d}}" class="loading-cover"><uni-load-more wx:if="{{e}}" class="load-more" u-i="fb9d0f82-0" bind:__l="__l" u-p="{{e}}"></uni-load-more></view><view wx:if="{{f}}" class="error-message"><text class="error-text">{{g}}</text></view></view></view>