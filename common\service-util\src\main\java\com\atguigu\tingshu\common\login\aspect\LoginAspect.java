package com.atguigu.tingshu.common.login.aspect;

import com.alibaba.fastjson.JSONObject;
import com.atguigu.tingshu.common.constant.PublicConstant;
import com.atguigu.tingshu.common.constant.RedisConstant;
import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.common.result.ResultCodeEnum;
import com.atguigu.tingshu.common.util.AuthContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.jwt.Jwt;
import org.springframework.security.jwt.JwtHelper;
import org.springframework.security.jwt.crypto.sign.RsaVerifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Aspect
@Component
public class LoginAspect {

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 定义切面逻辑(判断请求的资源是否登录, 如果登录 直接访问这个资源 反之去登录)
     * <p>
     * 切入点表达式有9种
     * <p>
     * <p>
     * 通知类型: 5种
     * 前置通知:
     * 特点: 切面逻辑在执行目标方法之前执行
     * 用法: @Before
     * <p>
     * <p>
     * 返回通知(获取不到执行目标的方法期间出现异常对象 但是可以拿到执行完目标方法的结果):
     * 特点: 切面逻辑在执行完目标方法之后(执行目标方法之前不会出现异常) 来执行到
     * 用法: @AfterReturning
     * <p>
     * <p>
     * 后置通知(不能捕获到执行目标方法期间出现的异常对象):
     * 特点: 切面逻辑在执行完目标方法之后(无论目标方法是否出现异常) 来执行到
     * 用法: @After
     * <p>
     * <p>
     * 异常通知(可以捕获到执行目标方法期间出现的异常对象 获取不到执行目标方法后的结果对象):
     * 特点: 切面逻辑在执行目标方法出现异常之后来执行到
     * 用法: @AfterThrowing
     * <p>
     * <p>
     * 环绕通知(ProceedingJoinPoint 即能够获取到执行目标方法执行期间的异常对象 也能够获取到目标方法执行后的结果对象):
     * 特点: 切面逻辑在执行目标方法之前和之后以及执行目标方法期间是否出现异常都会执行到
     * 用法: @Around
     *
     *
     * ThreadLocal在使用的过程中注意内存泄露的问题
     *
     * 内存泄露: 一个线程将对象(数据)存放到了内存的堆空间, 但是当这个线程干完活之后 没有将堆中刚刚放进去的对象(数据)清空,
     * 一次内存泄露可以不用管 但是久而久之 内存泄漏就会导致OOM(堆溢出)
     *
     * 使用ThreadLocal也不一定出现内存泄漏
     * 1. new Thread(()-> {
     *
     *    ThreadLocal<Long> threadLocal = new ThreadLocal<>();
     *    threadLocal.set(1L);
     * })
     */

    @Around(value = "@annotation(com.atguigu.tingshu.common.login.annotation.TingShuLogin)")
    public Object loginCheck(ProceedingJoinPoint pjp) throws Throwable {

        //前置通知
        //1. 获取请求中的令牌
        String jsonWebToken = getJsonWebToken();

        //2. 判断是否携带了令牌(如果携带令牌还将载荷中的数据获取到)
        Long userId = checkTokenAndGetUserId(jsonWebToken);

        //将认证中心获取到的userId放到ThreadLocal中
        AuthContextHolder.setUserId(userId);
//        ThreadLocal<Long> longThreadLocal = new ThreadLocal<>();
//        longThreadLocal.set(userId);

        Object retVal = null;
        try {
            retVal = pjp.proceed();
        } finally {
            AuthContextHolder.removeUserId(); // 解决内存泄漏
        }

        //返回通知
        return retVal;
    }

    private Long checkTokenAndGetUserId(String jsonWebTokenFromWeb) {

        //1. 校验是否该请求中携带了
        if (StringUtils.isEmpty(jsonWebTokenFromWeb)) {
            throw new GuiguException(ResultCodeEnum.LOGIN_AUTH);
        }

        //2. 校验jsonWebToken是否被篡改了
        Jwt jwt = JwtHelper.decodeAndVerify(jsonWebTokenFromWeb, new RsaVerifier(PublicConstant.PUBLIC_KEY));

        //3. 校验通过 获取载荷数据
        String claims = jwt.getClaims();

        @SuppressWarnings("unchecked")
        Map<String, Object> map = JSONObject.parseObject(claims, Map.class);
        String userId = String.valueOf(map.get("id"));
        String openId = String.valueOf(map.get("openId"));

        //4. 比对Redis中是否存在jsonWebToken
        String accessTokenKey = RedisConstant.USER_LOGIN_KEY_PREFIX + openId;
        String accessTokenFromRedis = redisTemplate.opsForValue().get(accessTokenKey);
        if (StringUtils.isEmpty(accessTokenFromRedis) || !jsonWebTokenFromWeb.equals(accessTokenFromRedis)) {
            throw new GuiguException(401, "accessToken已过期");
        }

        return Long.parseLong(userId);

    }

    private static String getJsonWebToken() {
        //1. 获取用户的身份信息
        //1.1 获取目标请求属性对象
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        //1.2 获取请求对象
        HttpServletRequest request = requestAttributes.getRequest();
        //1.3 获取请求对象的请求头
        return request.getHeader("token");
    }
}
