package com.atguigu.tingshu.account.service.impl;

import com.atguigu.tingshu.account.mapper.UserAccountMapper;
import com.atguigu.tingshu.account.service.MyOpsService;
import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.model.account.UserAccount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class MyOpsServiceImpl implements MyOpsService {

    @Autowired
    private UserAccountMapper  userAccountMapper;

    /**
     * 用户账户初始化
     * @param content
     */
    @Override
    public void userAccountRegister(String content) {
        try {
            UserAccount userAccount = new UserAccount();
            userAccount.setUserId(Long.parseLong(content));
            int insert = userAccountMapper.insert(userAccount);
            log.info("初始化用户账户, {}", insert > 0 ? "成功" : "失败");
        } catch (NumberFormatException e) {
            throw new GuiguException(201, "服务内部处理数据失败");
        }
    }
}
