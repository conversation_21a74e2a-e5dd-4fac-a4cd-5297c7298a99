package com.atguigu.tingshu.album.api;

import com.atguigu.tingshu.common.minio.config.MinioProperties;
import com.atguigu.tingshu.common.minio.service.FileUploadService;
import com.atguigu.tingshu.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Tag(name = "上传管理接口")
@RestController
@RequestMapping("api/album")
public class FileUploadApiController {

    @Autowired
    private MinioProperties minioProperties;

    @Autowired
    private FileUploadService fileUploadService;

    //Request URL: http://localhost:8500/api/album/fileUpload
    @PostMapping("/fileUpload")
    @Operation(summary = "图片文件上传")
    public Result fileUpload(MultipartFile file) throws IOException, NoSuchAlgorithmException, InvalidKeyException {

        String picUrl = fileUploadService.fileUploadService(file);
        System.out.println(file.getOriginalFilename());
        return Result.ok(picUrl);
    }

}
