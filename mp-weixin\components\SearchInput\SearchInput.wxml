<view class="{{['gui-flex', 'gui-row', 'gui-nowrap', 'gui-align-items-center', 'gui-flex1', 'data-v-5f0fcaba', B]}}" style="{{'height:' + C + ';' + ('border-radius:' + D)}}"><text class="gui-icons gui-block gui-text-center gui-color-gray data-v-5f0fcaba" catchtap="{{a}}" style="{{'font-size:' + b + ';' + ('line-height:' + c) + ';' + ('width:' + d) + ';' + ('margin-left:' + '12rpx')}}"></text><input wx:if="{{e}}" type="text" placeholder-class="{{f}}" class="gui-search-input gui-flex1 gui-primary-text data-v-5f0fcaba" placeholder="{{g}}" focus="{{h}}" style="{{'height:' + i + ';' + ('line-height:' + j) + ';' + ('font-size:' + k)}}" bindblur="{{l}}" bindinput="{{m}}" bindconfirm="{{n}}" value="{{o}}"/><text wx:if="{{p}}" class="gui-search-input gui-flex1 gui-block gui-color-gray data-v-5f0fcaba" catchtap="{{r}}" style="{{'height:' + s + ';' + ('line-height:' + t) + ';' + ('font-size:' + v)}}">{{q}}</text><text wx:if="{{w}}" class="gui-search-icon gui-icons gui-block gui-text-center gui-color-gray data-v-5f0fcaba" catchtap="{{x}}" style="{{'font-size:' + y + ';' + ('line-height:' + z) + ';' + ('width:' + A) + ';' + ('margin-right:' + '5rpx')}}"></text></view>