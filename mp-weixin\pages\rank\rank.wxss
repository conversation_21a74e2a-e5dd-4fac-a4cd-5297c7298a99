/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.gui-wrapper.data-v-716fbf2c {
  display: flex;
  flex-direction: column;
}
.gui-menu-wrap.data-v-716fbf2c {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.gui-tab-view.data-v-716fbf2c {
  width: 200rpx;
  height: 100%;
  background-color: #f6f6f6;
}
.gui-tab-item.data-v-716fbf2c {
  height: 100rpx;
  background: #f6f6f6;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #444;
  font-weight: 400;
  line-height: 1;
}
.gui-tab-item-active.data-v-716fbf2c {
  position: relative;
  color: #000;
  font-weight: 600;
  background: #fff;
}
.gui-tab-item-active.data-v-716fbf2c::before {
  content: "";
  position: absolute;
  border-left: 4px solid red;
  height: 32rpx;
  left: 0;
  top: 39rpx;
}
.gui-tab-view.data-v-716fbf2c {
  height: 100%;
}
.gui-right-box.data-v-716fbf2c {
  background-color: #f6f6f6;
}
.gui-page-view.data-v-716fbf2c {
  padding: 16rpx;
}
.gui-class-item.data-v-716fbf2c {
  margin-bottom: 30rpx;
  padding: 16rpx;
  border-radius: 8rpx;
}
.gui-class-item.data-v-716fbf2c:last-child {
  min-height: 100vh;
}
.gui-item-title.data-v-716fbf2c {
  font-size: 26rpx;
  color: gray;
  font-weight: bold;
}
.gui-item-menu-name.data-v-716fbf2c {
  font-weight: normal;
  font-size: 30rpx;
}
.gui-item-container.data-v-716fbf2c {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.gui-thumb-box.data-v-716fbf2c {
  width: 240rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 80rpx;
  padding: 30rpx 10rpx;
  background-color: #fff;
  border-radius: 8rpx;
  color: rgba(0, 0, 0, 0.65);
}