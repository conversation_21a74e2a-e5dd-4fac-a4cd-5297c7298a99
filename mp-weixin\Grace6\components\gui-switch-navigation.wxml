<scroll-view scroll-with-animation="{{k}}" scroll-x="{{true}}" show-scrollbar="{{false}}" class="{{['data-v-7f73495d', 'gui-scroll-x', l]}}" style="{{'width:' + m}}" scroll-into-view="{{n}}" scroll-left="{{o}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="p" class="gui-scroll-x-items gui-columns data-v-7f73495d" id="{{item.o}}" style="{{'width:' + g + ';' + ('margin-right:' + h) + ';' + ('padding-left:' + i) + ';' + ('padding-right:' + j)}}" bindtap="{{item.q}}" data-index="{{item.r}}"><view class="{{['gui-flex', 'gui-nowrap', 'gui-align-items-start', 'data-v-7f73495d', e]}}"><text class="{{['gui-block', 'gui-border-box', 'data-v-7f73495d', b, item.b]}}" style="{{'text-align:' + c + ';' + ('line-height:' + d) + ';' + ('font-size:' + item.c) + ';' + ('font-weight:' + item.d)}}">{{item.a}}</text><view wx:if="{{item.e}}" class="data-v-7f73495d"><text wx:if="{{item.f}}" class="gui-nav-tips gui-block data-v-7f73495d" style="{{item.h}}">{{item.g}}</text><text wx:else class="gui-nav-tips gui-block data-v-7f73495d" style="{{item.i}}"></text></view></view><view class="gui-flex gui-rows data-v-7f73495d" style="{{'justify-content:' + f}}"><view wx:if="{{item.j}}" class="{{['nav-active-line', 'data-v-7f73495d', item.k]}}" style="{{'width:' + item.l + ';' + ('height:' + item.m) + ';' + ('border-radius:' + item.n)}}"></view></view></view></scroll-view>