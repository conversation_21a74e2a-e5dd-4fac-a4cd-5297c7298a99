<view class="{{['gui-flex', 'gui-row', 'gui-nowrap', 'gui-align-items-center', 'gui-flex1', 'data-v-c34f888c', A]}}" style="{{'height:' + B + ';' + ('border-radius:' + C)}}"><text class="gui-icons gui-block gui-text-center gui-color-gray data-v-c34f888c" catchtap="{{a}}" style="{{'font-size:' + b + ';' + ('line-height:' + c) + ';' + ('width:' + d) + ';' + ('margin-left:' + '12rpx')}}"></text><input wx:if="{{e}}" type="text" placeholder-class="{{f}}" class="gui-search-input gui-flex1 gui-primary-text data-v-c34f888c" placeholder="{{g}}" focus="{{h}}" style="{{'height:' + i + ';' + ('line-height:' + j) + ';' + ('font-size:' + k)}}" bindinput="{{l}}" bindconfirm="{{m}}" value="{{n}}"/><text wx:if="{{o}}" class="gui-search-input gui-flex1 gui-block gui-color-gray data-v-c34f888c" catchtap="{{q}}" style="{{'height:' + r + ';' + ('line-height:' + s) + ';' + ('font-size:' + t)}}">{{p}}</text><text wx:if="{{v}}" class="gui-search-icon gui-icons gui-block gui-text-center gui-color-gray data-v-c34f888c" catchtap="{{w}}" style="{{'font-size:' + x + ';' + ('line-height:' + y) + ';' + ('width:' + z) + ';' + ('margin-right:' + '5rpx')}}"></text></view>