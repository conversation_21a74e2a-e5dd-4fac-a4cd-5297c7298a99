<view class="{{['uni-easyinput', aa && 'uni-easyinput-error']}}" style="{{ab}}"><view class="{{['uni-easyinput__content', Y]}}" style="{{Z}}"><uni-icons wx:if="{{a}}" class="content-clear-icon" bindclick="{{b}}" u-i="9635fe68-0" bind:__l="__l" u-p="{{c}}"></uni-icons><textarea wx:if="{{d}}" class="{{['uni-easyinput__content-textarea', e && 'input-padding']}}" name="{{f}}" value="{{g}}" placeholder="{{h}}" placeholderStyle="{{i}}" disabled="{{j}}" placeholder-class="uni-easyinput__placeholder-class" maxlength="{{k}}" focus="{{l}}" autoHeight="{{m}}" cursor-spacing="{{n}}" bindinput="{{o}}" bindblur="{{p}}" bindfocus="{{q}}" bindconfirm="{{r}}" bindkeyboardheightchange="{{s}}"></textarea><block wx:else><input wx:if="{{r0}}" type="{{t}}" class="uni-easyinput__content-input" style="{{v}}" name="{{w}}" value="{{x}}" password="{{y}}" placeholder="{{z}}" placeholderStyle="{{A}}" placeholder-class="uni-easyinput__placeholder-class" disabled="{{B}}" maxlength="{{C}}" focus="{{D}}" confirmType="{{E}}" cursor-spacing="{{F}}" bindfocus="{{G}}" bindblur="{{H}}" bindinput="{{I}}" bindconfirm="{{J}}" bindkeyboardheightchange="{{K}}"/></block><block wx:if="{{L}}"><uni-icons wx:if="{{M}}" class="{{['content-clear-icon', N && 'is-textarea-icon']}}" bindclick="{{O}}" u-i="9635fe68-1" bind:__l="__l" u-p="{{P}}"></uni-icons></block><block wx:elif="{{Q}}"><uni-icons wx:if="{{R}}" class="content-clear-icon" bindclick="{{S}}" u-i="9635fe68-2" bind:__l="__l" u-p="{{T}}"></uni-icons></block><block wx:else><uni-icons wx:if="{{U}}" class="{{['content-clear-icon', V && 'is-textarea-icon']}}" bindclick="{{W}}" u-i="9635fe68-3" bind:__l="__l" u-p="{{X}}"></uni-icons></block><slot name="right"></slot></view></view>