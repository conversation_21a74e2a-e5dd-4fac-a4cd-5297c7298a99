<view><view class="gui-relative" style="background-color:#7d7d4b"><view class="gui-p-t-40 gui-flex gui-row"><view class="gui-flex gui-flex1 gui-row gui-justify-content-start gui-p-l-20 gui-align-items-center"><view class="gui-icons gui-color-white gui-m-r-40" bindtap="{{a}}"></view><gui-switch-navigation wx:if="{{c}}" bindchange="{{b}}" u-i="6ae8dc9e-0" bind:__l="__l" u-p="{{c}}"></gui-switch-navigation></view><view class="gui-flex gui-row gui-p-l-20 gui-p-r-20 gui-align-items-center"><text class="gui-icons gui-block gui-color-white gui-m-r-10"></text><text class="gui-text-small gui-color-white">分享</text></view></view><swiper class="tab-card-body" current="{{H}}" style="{{'height:' + I}}"><swiper-item><scroll-view scroll-y style="{{'height:' + y}}"><view class="gui-relative gui-flex gui-justify-content-center gui-m-l-100 gui-m-r-100 gui-m-t-30 gui-m-b-30"><view class="gui-flex gui-absolute-lb gui-bg-black-opacity7 gui-p-l-5 gui-p-r-5 gui-text-small gui-color-white gui-p-t-5 gui-p-b-5 gui-p-l-20 gui-p-r-20"><text class="gui-icons gui-block gui-color-drak gui-m-r-5 gui-p-t-5"></text><text>{{d}}</text></view><image class="gui-border-radius" mode="aspectFill" src="{{e}}"></image></view><view class="gui-flex gui-column gui-align-items-center gui-justify-content-center gui-m-l-50 gui-m-r-50 gui-m-t-30 gui-m-b-30"><view class="gui-flex gui-row gui-align-items-center gui-m-t-20"><text>{{f}}</text></view></view><view class="gui-flex gui-row gui-space-between gui-align-items-center gui-m-l-50 gui-m-r-50 gui-m-t-30 gui-m-b-30"><text class="iconfont gui-m-r-5 gui-p-t-5 gui-color-white gui-h5">{{g}}</text><view class="gui-flex1 gui-m-l-20 gui-m-r-20" style="width:100rpx"><slider step="1" activeColor="#f86442" block-color="#fff" block-size="10" min="{{0}}" max="{{h}}" value="{{i}}" bindchange="{{j}}" bindtouchstart="{{k}}" bindtouchend="{{l}}"/></view><text class="iconfont gui-m-r-5 gui-p-t-5 gui-color-white gui-h5" style="width:100rpx">{{m}}</text></view><view class="gui-flex gui-row gui-space-between gui-m-l-50 gui-m-r-50 gui-m-t-30 gui-m-b-30 gui-align-items-center"><text class="gui-icons gui-m-r-5 gui-p-t-5 gui-color-white gui-h5" bindtap="{{n}}"></text><text class="gui-icons gui-m-r-5 gui-p-t-5 gui-color-white gui-h2" bindtap="{{o}}"></text><uni-icons wx:if="{{p}}" class="iconfont gui-p-t-5 gui-color-white gui-h1" bindclick="{{q}}" u-i="6ae8dc9e-1" bind:__l="__l" u-p="{{r}}"></uni-icons><text wx:else class="iconfont gui-p-t-5 gui-color-white gui-h1" bindtap="{{s}}"></text><text class="gui-icons gui-m-r-5 gui-p-t-5 gui-color-white gui-h2" bindtap="{{t}}"></text><text class="gui-icons gui-m-r-5 gui-p-t-5 gui-color-white gui-h5"></text></view><view class="gui-list-items gui-m-l-50 gui-m-r-50 gui-m-t-30 gui-m-b-30 gui-bg-black-opacity1 gui-p-20"><view class="gui-list-image"><image class="gui-list-image" mode="aspectFill" src="{{v}}"></image></view><view class="gui-list-body"><view class="gui-list-title"><text class="gui-text gui-block gui-secondary-text gui-text-left gui-ellipsis gui-color-white">{{w}}</text></view><text class="gui-list-body-desc gui-text-brown gui-m-t-10 gui-ellipsis">{{x}}人订阅</text></view><view class="gui-p-10 gui-border-radius gui-m-l-20 gui-bg-black-opacity2 gui-flex gui-align-items-center"><text class="gui-text-small gui-text-brown">免费订阅</text></view></view></scroll-view><view class="gui-absolute-lb gui-bg-black-opacity1 gui-p-t-20 gui-p-b-20 gui-color-white" style="width:100%"><view class="gui-flex gui-row gui-space-between gui-m-l-50 gui-m-r-50 gui-text-center"><view bindtap="{{z}}" class="gui-flex gui-flex1 gui-row gui-bg-black-opacity1 gui-border-radius gui-p-t-10 gui-p-b-10 gui-p-l-30 gui-p-r-30 gui-align-items-center"><text class="gui-icons gui-m-b-5 gui-m-r-10"></text><text>发表评论</text></view><view class="gui-flex gui-margin-x gui-column gui-align-items-center" bindtap="{{C}}"><text wx:if="{{A}}" class="gui-icons gui-h3 gui-m-b-5"></text><text wx:else class="gui-icons gui-h3 gui-m-b-5"></text><text class="gui-text-small">{{B}}</text></view><view class="gui-flex gui-column gui-align-items-center" bindtap="{{E}}"><text class="gui-icons gui-h3 gui-m-b-5"></text><text class="gui-text-small">{{D}}</text></view></view></view></swiper-item><swiper-item><comment-list wx:if="{{G}}" style="{{'height:' + F}}" u-i="6ae8dc9e-2" bind:__l="__l" u-p="{{G}}"></comment-list><view class=""></view></swiper-item></swiper></view><uni-popup wx:if="{{Q}}" class="r" u-s="{{['d']}}" u-r="albumPopupRef" u-i="6ae8dc9e-3" bind:__l="__l" u-p="{{Q}}"><view class="header"><view class="title">播放列表</view><view class="cancel-btn" bindtap="{{J}}">取消</view></view><z-paging wx:if="{{O}}" class="r" u-s="{{['d']}}" u-r="zPagingRef" bindquery="{{M}}" u-i="6ae8dc9e-4,6ae8dc9e-3" bind:__l="__l" bindupdateModelValue="{{N}}" u-p="{{O}}"><view class="audio-list"><view wx:for="{{K}}" wx:for-item="item" wx:key="i" class="gui-list-items" bindtap="{{item.j}}"><view class="gui-relative track-item-sort"><view class="{{[item.b, 'gui-h5']}}">{{item.a}}</view></view><view class="gui-list-body gui-border-b"><view class="gui-list-title"><text class="{{[item.d, 'gui-list-title-text', 'gui-ellipsis']}}">{{item.c}}</text></view><view class="gui-color-gray gui-flex gui-text-small gui-flex gui-align-items-center gui-m-t-20"><text wx:if="{{item.e}}" class="gui-icons gui-block gui-m-r-10"></text><text wx:else class="gui-icons gui-block gui-m-r-10"></text><text class="gui-block gui-m-r-20">{{item.f}}</text><text class="gui-icons gui-block gui-m-r-10"></text><text class="gui-block gui-m-r-20">{{item.g}}</text><text class="gui-icons gui-block gui-m-r-10"></text><text class="gui-block">{{item.h}}</text></view></view></view></view></z-paging></uni-popup></view>