package com.atguigu.tingshu.album.service.impl;

import com.atguigu.tingshu.album.mapper.*;
import com.atguigu.tingshu.album.service.BaseCategoryService;
import com.atguigu.tingshu.model.album.BaseAttribute;
import com.atguigu.tingshu.model.album.BaseCategory1;
import com.atguigu.tingshu.model.album.BaseCategoryView;
import com.atguigu.tingshu.model.category.CategoryVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class BaseCategoryServiceImpl extends ServiceImpl<BaseCategory1Mapper, BaseCategory1> implements BaseCategoryService {

	@Autowired
	private BaseCategory1Mapper baseCategory1Mapper;

	@Autowired
	private BaseCategory2Mapper baseCategory2Mapper;

	@Autowired
	private BaseCategory3Mapper baseCategory3Mapper;

	@Autowired
	private BaseAttributeMapper baseAttributeMapper;


	@Autowired
	private BaseCategoryViewMapper  baseCategoryViewMapper;

	@Override
	public List<CategoryVo> getBaseCategoryList() {
		return baseCategory1Mapper.getBaseCategoryList();
	}

	@Override
	public List<BaseAttribute> findAttribute(Long category1Id) {
		return baseAttributeMapper.findAttribute(category1Id);
	}

//	//优化
//	//1. 使用递归
//	//2. sql层面(MyBatis持久层框架完成自动封装)
//	@Override
//	public List<CategoryVo> getBaseCategoryList() {
//
//		ArrayList<CategoryVo> result = new ArrayList<>();
//		//1. 查询所有的分类信息
//		List<BaseCategoryView> baseCategoryViews = baseCategoryViewMapper.selectList(null);
//
//		//2. 封装数据
//
//		Map<Long, List<BaseCategoryView>> category1IdAndValue = baseCategoryViews.stream().collect(Collectors.groupingBy(BaseCategoryView::getCategory1Id));
//
//		for (Map.Entry<Long, List<BaseCategoryView>> longListEntry : category1IdAndValue.entrySet()) {
//
//			//1. 定义一个一级分类
//			CategoryVo category1Vo = new CategoryVo();
//			category1Vo.setCategoryId(longListEntry.getKey()); // 给一级分类id赋值
//			category1Vo.setCategoryName(longListEntry.getValue().get(0).getCategory1Name()); // 给一级分类名称赋值
//
//			Map<Long, List<BaseCategoryView>> category2IdAndValue = longListEntry.getValue().stream().collect(Collectors.groupingBy(BaseCategoryView::getCategory2Id));
//
//			ArrayList<CategoryVo> category1Childes = new ArrayList<>();
//
//			for (Map.Entry<Long, List<BaseCategoryView>> listEntry : category2IdAndValue.entrySet()) {
//				//2. 定义一个二级分类
//				CategoryVo category2Vo = new CategoryVo();
//				category2Vo.setCategoryId(listEntry.getKey()); // 给二级分类id赋值
//				category2Vo.setCategoryName(listEntry.getValue().get(0).getCategory2Name()); // 给二级分类名称赋值
//
//				Map<Long, List<BaseCategoryView>> category3IdAndValue = listEntry.getValue().stream().collect(Collectors.groupingBy(BaseCategoryView::getCategory3Id));
//
//				ArrayList<CategoryVo> category2Childes = new ArrayList<>();
//				for (BaseCategoryView baseCategoryView : listEntry.getValue()) {
//					CategoryVo category3Vo = new CategoryVo();
//					category3Vo.setCategoryId(baseCategoryView.getCategory3Id()); // 给三级分类id赋值
//					category3Vo.setCategoryName(baseCategoryView.getCategory3Name()); // 给三级分类名称赋值
//					category3Vo.setCategoryChild(null);
//					category2Childes.add(category3Vo);
//				}
//
//				category2Vo.setCategoryChild(category2Childes); // 给二级分类孩子赋值
//				category1Childes.add(category2Vo);
//			}
//
//			category1Vo.setCategoryChild(category1Childes); // 给一级分类孩子赋值
//			result.add(category1Vo); // 将一级分类放入到结果集中
//		}
//		return result;
//	}
}
