<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tingshu_parent241028</artifactId>
        <groupId>com.atguigu.tingshu</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service</artifactId>
    <packaging>pom</packaging>
    <description>应用的业务层</description>

    <modules>
        <module>service-album</module>
        <module>service-search</module>
        <module>service-account</module>
        <module>service-dispatch</module>
        <module>service-order</module>
        <module>service-payment</module>
        <module>service-user</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>service-util</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>service-account-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>service-album-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>service-order-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>service-user-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>service-search-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>