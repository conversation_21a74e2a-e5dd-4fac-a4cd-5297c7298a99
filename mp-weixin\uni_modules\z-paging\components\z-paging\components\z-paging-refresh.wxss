/* [z-paging]公用的静态css资源 */
.zp-line-loading-image.data-v-00a16504 {
	margin-right: 8rpx;
	width: 28rpx;
	height: 28rpx;

	animation: loading-flower-00a16504 1s steps(12) infinite;

	color: #666666;
}
.zp-loading-image-ios.data-v-00a16504{
	width: 20px;
	height: 20px;
}
.zp-loading-image-android.data-v-00a16504{
	width: 32rpx;
	height: 32rpx;
}
@keyframes loading-flower-00a16504 {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}
.zp-r-container.data-v-00a16504 {

		display: flex;
		height: 100%;

		flex-direction: row;
		justify-content: center;
		align-items: center;
}
.zp-r-container-padding.data-v-00a16504 {
}
.zp-r-left.data-v-00a16504 {

		display: flex;

		flex-direction: row;
		align-items: center;
		overflow: hidden;
}
.zp-r-left-image.data-v-00a16504 {
		transition-duration: .2s;
		transition-property: transform;
		color: #666666;
}
.zp-r-left-image-pre-size.data-v-00a16504{

		width: 30rpx;
		height: 30rpx;
		overflow: hidden;
}
.zp-r-arrow-top.data-v-00a16504 {
		transform: rotate(0deg);
}
.zp-r-arrow-down.data-v-00a16504 {
		transform: rotate(180deg);
}
.zp-r-right.data-v-00a16504 {
		font-size: 27rpx;

		display: flex;

		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.zp-r-right-text.data-v-00a16504 {
}
.zp-r-right-time-text.data-v-00a16504 {
		margin-top: 10rpx;
		font-size: 24rpx;
}
