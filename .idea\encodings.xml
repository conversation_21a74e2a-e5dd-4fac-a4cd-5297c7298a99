<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding" defaultCharsetForPropertiesFiles="UTF-8">
    <file url="file://$PROJECT_DIR$/common/common-log/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/common-util/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/rabbit-util/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/service-util/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/spring-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/model/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/server-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/service-account-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/service-album-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/service-order-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/service-search-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/service-system-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/service-user-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-account/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-album/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-comment/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-dispatch/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-live/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-order/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-payment/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-search/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/service-user/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="file://D:/develop/maven/repository1/org/springframework/boot/spring-boot-starter-parent/3.0.5/src/main/resources" charset="UTF-8" />
    <file url="PROJECT" charset="UTF-8" />
  </component>
</project>