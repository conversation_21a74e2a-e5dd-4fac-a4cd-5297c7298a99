<view class="gui-page-refresh gui-flex gui-column gui-justify-content-center data-v-adbb42b5" style="{{'height:' + x}}"><view style="{{'height:' + w}}" class="gui-flex gui-row gui-justify-content-center gui-align-items-center data-v-adbb42b5"><text wx:if="{{a}}" class="{{['gui-icons', 'gui-block', 'data-v-adbb42b5', b]}}" style="{{'font-size:' + c + ';' + ('width:' + d) + ';' + ('height:' + e) + ';' + ('line-height:' + f)}}"></text><text wx:if="{{g}}" ref="loadingIcon" class="{{['gui-icons', 'gui-block', 'gui-text-center', 'gui-rotate360', 'data-v-adbb42b5', h]}}" style="{{'font-size:' + i + ';' + ('width:' + j) + ';' + ('height:' + k) + ';' + ('line-height:' + l)}}"></text><text wx:if="{{m}}" class="{{['gui-icons', 'data-v-adbb42b5', n]}}" style="{{'font-size:' + o + ';' + ('width:' + p) + ';' + ('height:' + q) + ';' + ('line-height:' + r)}}"></text><text class="{{['gui-page-refresh-text', 'gui-block', 'data-v-adbb42b5', t]}}" style="{{'margin-left:12rpx' + ';' + ('font-size:' + v)}}">{{s}}</text></view></view>