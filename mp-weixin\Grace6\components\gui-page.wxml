<view style="{{'opacity:' + G}}" class="{{['gui-sbody', 'gui-flex', 'gui-column', H]}}"><view wx:if="{{a}}" class="{{['gui-header', 'gui-transition-all', e]}}" id="guiPageHeader" ref="guiPageHeader"><view class="{{['gui-page-status-bar', b]}}" style="{{'height:' + c}}"><slot name="gStatusBar"></slot></view><view class="gui-flex gui-column gui-justify-content-center" catchtap="{{d}}"><slot name="gHeader"></slot></view></view><view wx:if="{{f}}" style="{{'height:' + g}}"></view><view wx:if="{{h}}" id="guiPageBody" class="{{['gui-flex', 'gui-column', 'gui-relative', i]}}" ref="guiPageBody"><slot name="gBody"></slot></view><view wx:if="{{j}}" class="gui-flex1 gui-relative" id="guiPageBody" ref="guiPageBody" style="{{'margin-top:' + x}}"><scroll-view class="gui-absolute-full" scroll-y="{{true}}" show-scrollbar="{{false}}" bindtouchstart="{{q}}" bindtouchmove="{{r}}" bindtouchend="{{s}}" bindscroll="{{t}}" scroll-into-view="{{v}}" scroll-with-animation="{{false}}" bindscrolltolower="{{w}}"><view id="guiPageBodyTopTag"><gui-refresh wx:if="{{m}}" class="r" u-r="guiPageRefresh" bindreload="{{l}}" u-i="450804ec-0" bind:__l="__l" u-p="{{m}}"></gui-refresh></view><slot name="gBody"></slot><view wx:if="{{n}}"><gui-loadmore wx:if="{{p}}" class="r" u-r="guipageloadmore" u-i="450804ec-1" bind:__l="__l" u-p="{{p}}"></gui-loadmore></view></scroll-view></view><view wx:if="{{y}}" style="{{'height:' + z}}"></view><view wx:if="{{A}}" id="guiPageFooter" ref="guiPageFooter" class="{{['gui-page-footer', 'gui-border-box', C]}}"><slot name="gFooter"></slot><gui-iphone-bottom wx:if="{{B}}" u-i="450804ec-2" bind:__l="__l" u-p="{{B}}"></gui-iphone-bottom></view><view class="{{['gui-page-pendant', D]}}"><slot name="gPendant"></slot></view><view class="gui-page-fixed-top" ref="guiPageFixedTop" id="guiPageFixedTop" style="{{'top:' + E}}"><slot name="gFixedTop"></slot></view><gui-page-loading class="r" u-r="guipageloading" u-i="450804ec-3" bind:__l="__l"></gui-page-loading></view>