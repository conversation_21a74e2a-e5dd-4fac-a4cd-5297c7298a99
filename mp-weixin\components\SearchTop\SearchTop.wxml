<view><view class="search-container"><view wx:if="{{a}}" class="search-input-container"><search-input wx:if="{{g}}" u-r="searchInputRef" class="gui-flex1 search-input r" bindhandleBlur="{{c}}" bindclear="{{d}}" bindinputting="{{e}}" bindconfirm="{{f}}" u-i="0a883cf5-0" bind:__l="__l" u-p="{{g}}"></search-input><button bindtap="{{h}}" type="default" class="gui-button gui-button-mini gui-bg-blue gui-noborder" style="width:100rpx"><text class="gui-color-white gui-button-text-mini gui-icons">搜索 </text></button></view><view wx:if="{{i}}" class="search-suggestion-container gui-padding-x"><scroll-view show-scrollbar="{{false}}" scroll-y="{{true}}" style="max-height:500rpx" scroll-top="{{0}}"><view wx:for="{{j}}" wx:for-item="item" wx:key="c" class="search-suggestions-item" bindtap="{{item.b}}"><view class="search-suggest-text">{{item.a}}</view></view></scroll-view></view></view><view wx:if="{{k}}" class="search-history-container gui-padding"><view class="search-history-title gui-flex gui-rows gui-nowrap gui-align-items-center"><text class="gui-primary-text gui-h6 gui-flex1 gui-bold">搜索历史</text><text bindtap="{{l}}" class="gui-icons gui-color-gray"></text></view><view class="search-history-item-container gui-flex gui-rows gui-wrap"><view wx:for="{{m}}" wx:for-item="item" wx:key="b" bindtap="{{item.c}}" class="search-history-item gui-color-grey1 gui-text-small">{{item.a}}</view></view></view><le-dropdown wx:if="{{n}}" bindonConfirm="{{o}}" u-i="0a883cf5-1" bind:__l="__l" bindupdateMenuList="{{p}}" u-p="{{q}}"></le-dropdown><le-dropdown wx:if="{{r}}" bindonConfirm="{{s}}" u-i="0a883cf5-2" bind:__l="__l" bindupdateMenuList="{{t}}" u-p="{{v}}"></le-dropdown><le-dropdown wx:if="{{w}}" bindonConfirm="{{x}}" u-i="0a883cf5-3" bind:__l="__l" bindupdateMenuList="{{y}}" u-p="{{z}}"></le-dropdown></view>