<view class="uni-date"><view class="uni-date-editor" bindtap="{{m}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><view class="{{['uni-date-editor--x', k && 'uni-date-editor--x__disabled', l && 'uni-date-x--border']}}"><view wx:if="{{a}}" class="uni-date-x uni-date-single"><uni-icons wx:if="{{b}}" class="icon-calendar" u-i="19e308b8-0" bind:__l="__l" u-p="{{b}}"></uni-icons><view class="uni-date__x-input">{{c}}</view></view><view wx:else class="uni-date-x uni-date-range"><uni-icons wx:if="{{d}}" class="icon-calendar" u-i="19e308b8-1" bind:__l="__l" u-p="{{d}}"></uni-icons><view class="uni-date__x-input text-center">{{e}}</view><view class="range-separator">{{f}}</view><view class="uni-date__x-input text-center">{{g}}</view></view><view wx:if="{{h}}" class="uni-date__icon-clear" catchtap="{{j}}"><uni-icons wx:if="{{i}}" u-i="19e308b8-2" bind:__l="__l" u-p="{{i}}"></uni-icons></view></view></block></view><view hidden="{{!n}}" class="uni-date-mask--pc" bindtap="{{o}}"></view><view wx:if="{{p}}" hidden="{{!ar}}" ref="datePicker" class="uni-date-picker__container"><view wx:if="{{q}}" class="uni-date-single--x" style="{{I}}"><view class="uni-popper__arrow"></view><view wx:if="{{r}}" class="uni-date-changed popup-x-header"><input class="uni-date__input text-center" type="text" placeholder="{{s}}" value="{{t}}" bindinput="{{v}}"/><time-picker wx:if="{{B}}" u-s="{{['d']}}" style="width:100%" u-i="19e308b8-3" bind:__l="__l" bindupdateModelValue="{{A}}" u-p="{{B}}"><input class="uni-date__input text-center" type="text" placeholder="{{w}}" disabled="{{x}}" value="{{y}}" bindinput="{{z}}"/></time-picker></view><calendar wx:if="{{E}}" class="r" u-r="pcSingle" bindchange="{{D}}" style="padding:0 8px" u-i="19e308b8-4" bind:__l="__l" u-p="{{E}}"/><view wx:if="{{F}}" class="popup-x-footer"><text class="confirm-text" bindtap="{{H}}">{{G}}</text></view></view><view wx:else class="uni-date-range--x" style="{{aq}}"><view class="uni-popper__arrow"></view><view wx:if="{{J}}" class="popup-x-header uni-date-changed"><view class="popup-x-header--datetime"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{K}}" value="{{L}}" bindinput="{{M}}"/><time-picker wx:if="{{S}}" u-s="{{['d']}}" u-i="19e308b8-5" bind:__l="__l" bindupdateModelValue="{{R}}" u-p="{{S}}"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{N}}" disabled="{{O}}" value="{{P}}" bindinput="{{Q}}"/></time-picker></view><uni-icons wx:if="{{T}}" style="line-height:40px" u-i="19e308b8-6" bind:__l="__l" u-p="{{T}}"></uni-icons><view class="popup-x-header--datetime"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{U}}" value="{{V}}" bindinput="{{W}}"/><time-picker wx:if="{{ac}}" u-s="{{['d']}}" u-i="19e308b8-7" bind:__l="__l" bindupdateModelValue="{{ab}}" u-p="{{ac}}"><input class="uni-date__input uni-date-range__input" type="text" placeholder="{{X}}" disabled="{{Y}}" value="{{Z}}" bindinput="{{aa}}"/></time-picker></view></view><view class="popup-x-body"><calendar wx:if="{{ag}}" class="r" u-r="left" bindchange="{{ae}}" bindfirstEnterCale="{{af}}" style="padding:0 8px" u-i="19e308b8-8" bind:__l="__l" u-p="{{ag}}"/><calendar wx:if="{{ak}}" class="r" u-r="right" bindchange="{{ai}}" bindfirstEnterCale="{{aj}}" style="padding:0 8px;border-left:1px solid #F1F1F1" u-i="19e308b8-9" bind:__l="__l" u-p="{{ak}}"/></view><view wx:if="{{al}}" class="popup-x-footer"><text bindtap="{{an}}">{{am}}</text><text class="confirm-text" bindtap="{{ap}}">{{ao}}</text></view></view></view><calendar wx:if="{{as}}" class="r" u-r="mobile" bindconfirm="{{av}}" bindmaskClose="{{aw}}" u-i="19e308b8-10" bind:__l="__l" u-p="{{ax}}"/></view>