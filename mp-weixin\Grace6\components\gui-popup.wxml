<view wx:if="{{a}}" class="data-v-c6cddaae"><view wx:if="{{b}}" class="{{['gui-popup', 'gui-flex', 'gui-column', 'gui-justify-content-center', 'gui-align-items-center', 'data-v-c6cddaae', g]}}" ref="guipopup" catchtap="{{h}}" catchtouchmove="{{i}}" style="{{'background-color:' + j + ';' + ('z-index:' + k) + ';' + ('top:' + l) + ';' + ('animation-duration:' + m)}}"><view catchtap="{{c}}" ref="guiPopupCenter" class="{{['gui-popup-content', 'gui-popup-center', 'data-v-c6cddaae', d]}}" style="{{'width:' + e + ';' + ('animation-duration:' + f)}}"><slot></slot></view></view><view wx:if="{{n}}" style="{{'background-color:' + r + ';' + ('z-index:' + s) + ';' + ('top:' + t) + ';' + ('animation-duration:' + v)}}" class="{{['gui-popup', 'gui-flex', 'gui-column', 'data-v-c6cddaae', w]}}" ref="guipopup" catchtap="{{x}}" catchtouchmove="{{y}}"><view catchtap="{{o}}" ref="guiPopupTop" class="{{['gui-popup-content', 'gui-popup-top', 'data-v-c6cddaae', p]}}" style="{{'animation-duration:' + q}}"><slot></slot></view></view><view wx:if="{{z}}" style="{{'background-color:' + D + ';' + ('z-index:' + E) + ';' + ('top:' + F) + ';' + ('animation-duration:' + G)}}" class="{{['gui-popup', 'gui-flex', 'gui-column', 'gui-justify-content-end', 'data-v-c6cddaae', H]}}" ref="guipopup" catchtap="{{I}}" catchtouchmove="{{J}}"><view catchtap="{{A}}" ref="guiPopupBottom" class="{{['gui-popup-content', 'gui-popup-bottom', 'data-v-c6cddaae', 'gui-dark-bg-level-3', B]}}" style="{{'animation-duration:' + C}}"><slot></slot></view></view><view wx:if="{{K}}" class="{{['gui-popup', 'gui-flex', 'gui-column', 'data-v-c6cddaae', P]}}" ref="guipopup" catchtap="{{Q}}" catchtouchmove="{{R}}" style="{{'background-color:' + S + ';' + ('z-index:' + T) + ';' + ('top:' + U) + ';' + ('animation-duration:' + V)}}"><view catchtap="{{L}}" ref="guiPopupLeft" class="{{['gui-popup-content', 'gui-flex1', 'gui-flex', 'gui-column', 'gui-popup-left', 'data-v-c6cddaae', M]}}" style="{{'width:' + N + ';' + ('animation-duration:' + O)}}"><slot></slot></view></view><view wx:if="{{W}}" class="{{['gui-popup', 'gui-flex', 'gui-column', 'gui-align-items-end', 'data-v-c6cddaae', ab]}}" ref="guipopup" catchtap="{{ac}}" catchtouchmove="{{ad}}" style="{{'background-color:' + ae + ';' + ('z-index:' + af) + ';' + ('top:' + ag) + ';' + ('animation-duration:' + ah)}}"><view catchtap="{{X}}" ref="guiPopupRight" class="{{['gui-popup-content', 'gui-flex1', 'gui-flex', 'gui-column', 'gui-popup-right', 'data-v-c6cddaae', Y]}}" style="{{'width:' + Z + ';' + ('animation-duration:' + aa)}}"><slot></slot></view></view></view>