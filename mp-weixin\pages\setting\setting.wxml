<gui-page class="data-v-018cdf56" u-s="{{['gBody']}}" u-i="018cdf56-0" bind:__l="__l"><view class="gui-padding-x gui-padding data-v-018cdf56" slot="gBody"><view class="setting-item data-v-018cdf56" bindtap="{{c}}"><uni-section wx:if="{{b}}" class="data-v-018cdf56" u-s="{{['right']}}" u-i="018cdf56-1,018cdf56-0" bind:__l="__l" u-p="{{b}}"><uni-icons class="data-v-018cdf56" u-i="018cdf56-2,018cdf56-1" bind:__l="__l" u-p="{{a}}" slot="right"></uni-icons></uni-section></view><view class="setting-item data-v-018cdf56" bindtap="{{f}}"><uni-section wx:if="{{e}}" class="data-v-018cdf56" u-s="{{['right']}}" u-i="018cdf56-3,018cdf56-0" bind:__l="__l" u-p="{{e}}"><uni-icons class="data-v-018cdf56" u-i="018cdf56-4,018cdf56-3" bind:__l="__l" u-p="{{d}}" slot="right"></uni-icons></uni-section></view></view></gui-page><uni-popup wx:if="{{j}}" class="r data-v-018cdf56" u-s="{{['d']}}" u-r="dialogRef" u-i="018cdf56-5" bind:__l="__l" u-p="{{j}}"><uni-popup-dialog wx:if="{{h}}" class="data-v-018cdf56" bindconfirm="{{g}}" u-i="018cdf56-6,018cdf56-5" bind:__l="__l" u-p="{{h}}"></uni-popup-dialog></uni-popup>