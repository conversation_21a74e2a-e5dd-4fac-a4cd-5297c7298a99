package com.atguigu.tingshu.album.service;

import com.atguigu.tingshu.model.album.BaseAttribute;
import com.atguigu.tingshu.model.album.BaseCategory1;
import com.atguigu.tingshu.model.category.CategoryVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface BaseCategoryService extends IService<BaseCategory1> {

    /**
     * 查询平台分类信息
     * @return
     */
    List<CategoryVo> getBaseCategoryList();

    /**
     * 根据一级分类查询专辑的标签信息
     * @param category1Id
     * @return
     */
    List<BaseAttribute> findAttribute(Long category1Id);
}
