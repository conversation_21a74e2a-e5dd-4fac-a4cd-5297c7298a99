<gui-page u-s="{{['gBody']}}" u-i="a63dfa1e-0" bind:__l="__l"><view class="gui-padding gui-padding-x gui-bg-white" slot="gBody"><uni-forms wx:if="{{j}}" class="r" u-s="{{['d']}}" u-r="formDataRef" u-i="a63dfa1e-1,a63dfa1e-0" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"><uni-forms-item wx:if="{{c}}" u-s="{{['d']}}" u-i="a63dfa1e-2,a63dfa1e-1" bind:__l="__l" u-p="{{c}}"><uni-easyinput wx:if="{{b}}" u-i="a63dfa1e-3,a63dfa1e-2" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"/></uni-forms-item><uni-forms-item wx:if="{{g}}" u-s="{{['d']}}" u-i="a63dfa1e-4,a63dfa1e-1" bind:__l="__l" u-p="{{g}}"><cl-upload wx:if="{{f}}" class="gui-flex gui-space-between" bindonSuccess="{{d}}" u-i="a63dfa1e-5,a63dfa1e-4" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"></cl-upload></uni-forms-item></uni-forms><button type="primary" bindtap="{{k}}">提交</button><view style="height:60rpx"></view></view></gui-page>