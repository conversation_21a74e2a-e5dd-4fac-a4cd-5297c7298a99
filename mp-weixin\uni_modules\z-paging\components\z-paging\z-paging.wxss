/* [z-paging]公共css*/
.z-paging-content.data-v-1aa372d7 {
	position: relative;

	display: flex;
	width: 100%;
	height: 100%;
	overflow: hidden;

	flex-direction: column;
}
.z-paging-content-fixed.data-v-1aa372d7, .zp-loading-fixed.data-v-1aa372d7 {
	position: fixed;

	height: auto;
	width: auto;

	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}
.zp-page-top.data-v-1aa372d7,.zp-page-bottom.data-v-1aa372d7 {

	width: auto;

	position: fixed;
	left: 0;
	right: 0;
	z-index: 999;
}
.zp-page-left.data-v-1aa372d7,.zp-page-right.data-v-1aa372d7{

	height: 100%;
}
.zp-scroll-view-super.data-v-1aa372d7 {
	flex: 1;
	position: relative;
}
.zp-view-super.data-v-1aa372d7{

	display: flex;

	flex-direction: row;
}
.zp-custom-refresher-container.data-v-1aa372d7 {
	overflow: hidden;
}
.zp-scroll-view-container.data-v-1aa372d7,.zp-scroll-view.data-v-1aa372d7 {
	position: relative;

	height: 100%;
	width: 100%;
}
.zp-absoulte.data-v-1aa372d7{

	position: absolute;
	top: 0;
	width: auto;
}
.zp-right.data-v-1aa372d7{
	right: 0;
}
.zp-scroll-view-absolute.data-v-1aa372d7 {
	position: absolute;
	top: 0;
	left: 0;
}
.zp-scroll-view-hide-scrollbar.data-v-1aa372d7 ::-webkit-scrollbar {
	display: none;
	-webkit-appearance: none;
	width: 0 !important;
	height: 0 !important;
	background: transparent;
}
.zp-paging-touch-view.data-v-1aa372d7 {
	width: 100%;
	height: 100%;
	position: relative;
}
.zp-fixed-bac-view.data-v-1aa372d7 {
	position: absolute;
	width: 100%;
	top: 0;
	left: 0;
	height: 200px;
}
.zp-paging-main.data-v-1aa372d7 {
	height: 100%;

	display: flex;

	flex-direction: column;
}
.zp-paging-container.data-v-1aa372d7 {
	flex: 1;
	position: relative;

	display: flex;

	flex-direction: column;
}
.zp-chat-record-loading-container.data-v-1aa372d7 {

	display: flex;
	width: 100%;




	align-items: center;
	justify-content: center;
	height: 60rpx;
	font-size: 26rpx;
}
.zp-chat-record-loading-custom-image.data-v-1aa372d7 {
	width: 35rpx;
	height: 35rpx;

	animation: loading-flower-1aa372d7 1s linear infinite;
}
.zp-custom-refresher-container.data-v-1aa372d7 {

	display: flex;

	flex-direction: row;
	justify-content: center;
	align-items: center;
}
.zp-back-to-top.data-v-1aa372d7 {
	width: 76rpx;
	height: 76rpx;
	z-index: 999;
	position: absolute;
	bottom: 0rpx;
	right: 25rpx;
	transition-duration: .3s;
	transition-property: opacity;
}
.zp-back-to-top-show.data-v-1aa372d7 {
	opacity: 1;
}
.zp-back-to-top-hide.data-v-1aa372d7 {
	opacity: 0;
}
.zp-back-to-top-img.data-v-1aa372d7 {

	width: 100%;
	height: 100%;




	z-index: 999;
}
.zp-empty-view.data-v-1aa372d7 {



	flex: 1;
}
.zp-empty-view-center.data-v-1aa372d7 {

	display: flex;

	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.zp-loading-fixed.data-v-1aa372d7 {
	z-index: 9999;
}
.zp-safe-area-inset-bottom.data-v-1aa372d7 {
	position: absolute;

	height: env(safe-area-inset-bottom);
}
.zp-n-refresh-container.data-v-1aa372d7 {

	display: flex;

	justify-content: center;
	width: 750rpx;
}
.zp-n-list-container.data-v-1aa372d7{

	display: flex;

	flex-direction: row;
	flex: 1;
}

/* [z-paging]公用的静态css资源 */
.zp-line-loading-image.data-v-1aa372d7 {
	margin-right: 8rpx;
	width: 28rpx;
	height: 28rpx;

	animation: loading-flower-1aa372d7 1s steps(12) infinite;

	color: #666666;
}
.zp-loading-image-ios.data-v-1aa372d7{
	width: 20px;
	height: 20px;
}
.zp-loading-image-android.data-v-1aa372d7{
	width: 32rpx;
	height: 32rpx;
}
@keyframes loading-flower-1aa372d7 {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}


