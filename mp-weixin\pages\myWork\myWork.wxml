<view class="content data-v-0d5ca095"><z-paging wx:if="{{n}}" class="r data-v-0d5ca095" u-s="{{['top','d']}}" u-r="zPagingRef" bindquery="{{l}}" u-i="0d5ca095-0" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"><view class="gui-dark-bg-level-3 gui-padding nav-top-container data-v-0d5ca095" slot="top"><view class=" data-v-0d5ca095"><gui-switch-navigation wx:if="{{b}}" class="data-v-0d5ca095" bindchange="{{a}}" u-i="0d5ca095-1,0d5ca095-0" bind:__l="__l" u-p="{{b}}"></gui-switch-navigation></view><view class="select-container data-v-0d5ca095"><gui-select-menu wx:if="{{e}}" class="r data-v-0d5ca095" u-r="guiSelectMenuRef" bindselect="{{d}}" u-i="0d5ca095-2,0d5ca095-0" bind:__l="__l" u-p="{{e}}"></gui-select-menu><view class="gui-text-small select-add data-v-0d5ca095" bindtap="{{f}}"><text class="gui-icons gui-block gui-color-gray gui-text data-v-0d5ca095"></text></view></view></view><block wx:if="{{g}}"><album-item-card wx:for="{{h}}" wx:for-item="item" wx:key="a" class="data-v-0d5ca095" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></album-item-card></block><block wx:if="{{i}}"><track-item-card wx:for="{{j}}" wx:for-item="item" wx:key="a" class="data-v-0d5ca095" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></track-item-card></block></z-paging></view>