<view class="content"><z-paging wx:if="{{h}}" class="r" u-s="{{['top','d']}}" u-r="zPagingRef" bindquery="{{f}}" u-i="954742d6-0" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"><view class="gui-flex gui-columns gui-justify-content-center gui-align-items-center gui-m-t-30 gui-bg-white" style="height:200rpx" slot="top"><view class="gui-flex-reply-container"><uni-easyinput wx:if="{{b}}" u-i="954742d6-1,954742d6-0" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"></uni-easyinput><text catchtap="{{c}}" class="gui-comments-replay-btn gui-block gui-bg-gray gui-dark-bg-level-2 gui-primary-text">提交 </text></view></view><view><view wx:for="{{d}}" wx:for-item="item" wx:key="s" class="gui-comments-items gui-flex gui-row gui-nowrap gui-space-between gui-bg-white gui-dark-bg-level-3"><image src="{{item.a}}" class="gui-comments-face"></image><view class="gui-comments-body"><view class="gui-flex gui-row gui-nowrap gui-space-between gui-align-items-center"><text class="gui-comments-header-text gui-text gui-primary-color">{{item.b}}</text><text bindtap="{{item.d}}" class="{{['gui-comments-header-text', 'gui-icons', 'gui-color-gray', 'gui-text-small', item.e]}}">  {{item.c}}</text></view><view bindtap="{{item.k}}" class="gui-comments-content gui-block gui-relative padding-r">{{item.f}} <uni-icons wx:if="{{item.g}}" bindclick="{{item.h}}" class="gui-absolute-rt" u-i="{{item.i}}" bind:__l="__l" u-p="{{item.j}}"></uni-icons></view><view wx:if="{{item.l}}"><view wx:for="{{item.m}}" wx:for-item="itemRe" wx:key="q" class="gui-comments-replay gui-block gui-bg-gray gui-dark-bg-level-2"><view wx:if="{{itemRe.a}}" bindtap="{{itemRe.h}}" class="gui-relative padding-r">{{itemRe.b}} : {{itemRe.c}} <uni-icons wx:if="{{itemRe.d}}" bindclick="{{itemRe.e}}" class="gui-absolute-rt" u-i="{{itemRe.f}}" bind:__l="__l" u-p="{{itemRe.g}}"></uni-icons></view><view wx:else catchtap="{{itemRe.p}}" class="gui-relative padding-r"><text>{{itemRe.i}}</text><text class="gui-color-gray gui-text-small" style="margin:0 6rpx">回复</text><text>{{itemRe.j}}</text><text>: {{itemRe.k}}</text><uni-icons wx:if="{{itemRe.l}}" bindclick="{{itemRe.m}}" class="gui-absolute-rt" u-i="{{itemRe.n}}" bind:__l="__l" u-p="{{itemRe.o}}"></uni-icons></view></view></view><view class="gui-comments-info gui-flex gui-rows gui-nowrap gui-align-items-center"><view class="gui-flex-reply-container"><uni-easyinput wx:if="{{item.p}}" u-i="{{item.n}}" bind:__l="__l" bindupdateModelValue="{{item.o}}" u-p="{{item.p}}"></uni-easyinput><text catchtap="{{item.q}}" class="gui-comments-replay-btn gui-block gui-bg-gray gui-dark-bg-level-2 gui-primary-text">回复 </text></view></view><view class="gui-comments-info-text gui-color-gray">{{item.r}}</view></view></view></view></z-paging></view>