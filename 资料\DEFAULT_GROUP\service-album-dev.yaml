server:
  port: 8501
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 查看日志
spring:
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  cloud:
    openfeign:
      lazy-attributes-resolution: true #开启懒加载，否则启动报错
      client:
        config:
          default:
            connectTimeout: 30000
            readTimeout: 30000
            loggerLevel: basic
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 1800000
      password:
  rabbitmq:
    host: ***************
    port: 5672
    username: guest
    password: guest
    publisher-confirm-type: CORRELATED
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual #默认情况下消息消费者是自动确认消息的，如果要手动确认消息则需要修改确认模式为manual
        prefetch: 1 # # 在prefetch=1的情况下，消费者在确认当前消息之前不会收到新的消息  在多个消费者下也不会将多个未确认的消息同时发送给同一个消费者。
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************
    username: root
    password: shangguigu@QY123
    hikari:
      connection-test-query: SELECT 1
      connection-timeout: 60000
      idle-timeout: 500000
      max-lifetime: 540000
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: GuliHikariPool
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 10MB     #单个文件最大限制
      max-request-size: 20MB  #多个文件最大限制
minio:
  endpointUrl: http://**************:9000
  accessKey: admin
  secretKey: admin123456
  bucketName: sph
vod:
  appId: 1307503602
  secretId: AKIDxjGYcR0KPFqBH8j6E7l9ICqFOO9fvwiE
  secretKey: KPFsGPJyNWQ5WheNq55qMcdOuABwSijU
  region: ap-guangzhou
  procedure: SimpleAesEncryptPreset #任务流
  tempPath: /root/tingshu/tempPath
  playKey: wrTwwu8U3DRSRDgC8l7q  #播放加密key
  bucketPrivate: daijia-private-1307503602  #数据万象绑定存储桶
  callbackUrl: http://m60dg45z4e4f.ngrok.xiaomiqiu123.top/api/album/trackInfo/notify  #数据万象音频审核回调地址
