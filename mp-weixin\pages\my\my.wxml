<gui-page wx:if="{{y}}" class="data-v-2f1ef635" u-s="{{['gHeader','gBody','gFooter']}}" u-i="2f1ef635-0" bind:__l="__l" u-p="{{y}}"><view style="height:44px" class="gui-flex data-v-2f1ef635" slot="gHeader"><view class="gui-flex gui-row gui-wrap gui-align-items-center gui-m-l-20 data-v-2f1ef635"><text class="gui-icons gui-block gui-color-drak gui-p-10 gui-b-50 gui-bg-black-opacity1 gui-m-r-10 data-v-2f1ef635"></text><text class="gui-icons gui-block gui-color-drak gui-p-10 gui-b-50 gui-bg-black-opacity1 gui-m-r-10 data-v-2f1ef635"></text><text bindtap="{{a}}" class="gui-icons gui-block gui-color-drak gui-p-10 gui-b-50 gui-bg-black-opacity1 data-v-2f1ef635"></text></view><view class="data-v-2f1ef635" style="width:200rpx"></view></view><view slot="gBody"><view class="gui-relative data-v-2f1ef635"><view class="gui-list gui-padding gui-bg-white gui-p-b-90 data-v-2f1ef635"><view class="gui-list-items gui-relative data-v-2f1ef635"><image mode="aspectFill" src="{{b}}" class="gui-card-img data-v-2f1ef635"></image><view class="gui-list-body gui-m-t-10 data-v-2f1ef635"><view class="gui-card-desc data-v-2f1ef635"><view class="gui-flex gui-rows gui-nowrap gui-align-items-center data-v-2f1ef635"><text class="gui-card-name gui-primary-text gui-h4 gui-bold data-v-2f1ef635">{{c}}</text><uni-icons wx:if="{{d}}" class="gui-m-r-10 data-v-2f1ef635" u-i="2f1ef635-1,2f1ef635-0" bind:__l="__l" u-p="{{e}}"></uni-icons></view></view></view><text class="gui-list-arrow-right gui-icons gui-color-gray data-v-2f1ef635" bindtap="{{f}}"></text></view></view><view class="gui-absolute-lb gui-bg-white gui-creation-live data-v-2f1ef635"><view class="gui-flex gui-row gui-nowrap gui-space-around gui-p-t-20 data-v-2f1ef635"><view bindtap="{{g}}" class="gui-flex gui-row gui-align-items-center data-v-2f1ef635"><text class="gui-icons gui-h3 gui-color-drak data-v-2f1ef635"></text><text class="gui-p-l-10 data-v-2f1ef635">创作中心</text></view><view bindtap="{{h}}" class="gui-flex gui-row gui-align-items-center data-v-2f1ef635"><text class="iconfont gui-color-drak gui-h3 data-v-2f1ef635"></text><text class="gui-p-l-10 data-v-2f1ef635">录音/直播</text></view></view></view></view><view class="gui-flex gui-space-between gui-bg-white gui-dark-bg-level-3 gui-padding gui-m-t-20 data-v-2f1ef635"><view class="gui-grid-item data-v-2f1ef635" bindtap="{{i}}"><text class="gui-grid-icon gui-icons gui-color-gray data-v-2f1ef635"></text><text class="gui-grid-text gui-icons gui-color-gray data-v-2f1ef635">VIP会员</text></view><view class="gui-grid-item data-v-2f1ef635" bindtap="{{j}}"><text class="gui-grid-icon gui-icons gui-color-gray data-v-2f1ef635"></text><text class="gui-grid-text gui-icons gui-color-gray data-v-2f1ef635">我的钱包</text></view><view class="gui-grid-item data-v-2f1ef635" bindtap="{{k}}"><text class="gui-grid-icon gui-icons gui-color-gray data-v-2f1ef635"></text><text class="gui-grid-text gui-icons gui-color-gray data-v-2f1ef635">我的订单</text></view></view><view class="gui-bg-white gui-dark-bg-level-3 gui-padding data-v-2f1ef635"><z-paging wx:if="{{x}}" class="r data-v-2f1ef635" u-s="{{['top','d']}}" u-r="zPagingRef" bindquery="{{v}}" u-i="2f1ef635-2,2f1ef635-0" bind:__l="__l" bindupdateModelValue="{{w}}" u-p="{{x}}"><view slot="top"><gui-switch-navigation wx:if="{{m}}" class="data-v-2f1ef635" bindchange="{{l}}" u-i="2f1ef635-3,2f1ef635-2" bind:__l="__l" u-p="{{m}}"></gui-switch-navigation><view class="data-v-2f1ef635" style="height:20rpx"></view></view><block wx:if="{{n}}"><subscribe-item-card wx:for="{{o}}" wx:for-item="item" wx:key="a" class="data-v-2f1ef635" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"/></block><block wx:if="{{p}}"><collect-and-history-track-item-card wx:for="{{q}}" wx:for-item="item" wx:key="a" class="data-v-2f1ef635" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></collect-and-history-track-item-card></block><block wx:if="{{r}}"><collect-and-history-track-item-card wx:for="{{s}}" wx:for-item="item" wx:key="a" class="data-v-2f1ef635" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></collect-and-history-track-item-card></block></z-paging></view></view><view slot="gFooter"><bottom-nav class="data-v-2f1ef635" u-i="2f1ef635-7,2f1ef635-0" bind:__l="__l"></bottom-nav></view></gui-page>