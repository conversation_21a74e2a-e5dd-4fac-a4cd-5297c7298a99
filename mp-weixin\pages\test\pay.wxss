/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.qr-box.data-v-dbd3e5f3 {
  width: 400rpx;
  height: 460rpx;
  margin: 0 auto;
  margin-top: 20rpx;
}
.pay-result.data-v-dbd3e5f3 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.pay-result .pay-status.data-v-dbd3e5f3 {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  font-size: 48rpx;
  line-height: 72rpx;
  font-weight: bold;
  color: #333333;
  display: flex;
  align-items: center;
}
.pay-result .pay-order.data-v-dbd3e5f3 {
  margin-top: 20rpx;
  font-size: 20rpx;
  line-height: 72rpx;
  color: #333333;
  display: flex;
  align-items: flex-start;
}
.pay-result .pay-status text.data-v-dbd3e5f3 {
  padding-left: 12rpx;
}
.pay-result .pay-money.data-v-dbd3e5f3 {
  color: #666666;
  font-size: 28rpx;
  line-height: 48rpx;
  margin-top: 28rpx;
  display: flex;
  align-items: baseline;
}
.pay-result .pay-money .pay-money__price.data-v-dbd3e5f3 {
  font-size: 36rpx;
  line-height: 48rpx;
  color: #fa4126;
}
.pay-result .btn-wrapper.data-v-dbd3e5f3 {
  margin-top: 200rpx;
  padding: 12rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}
.pay-result .btn-wrapper .status-btn.data-v-dbd3e5f3 {
  height: 88rpx;
  width: 334rpx;
  border-radius: 44rpx;
  border: 2rpx solid #fa4126;
  color: #fa4126;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 88rpx;
  text-align: center;
}