server:
  port: 8520 #account微服务的端口号

spring:
  rabbitmq:
    host: ***************
    port: 5672
    username: guest
    password: guest
    publisher-confirm-type: CORRELATED  # 发布确认模式
    publisher-returns: true  # 确保消息返回
    listener:
      simple:
        acknowledge-mode: manual #默认情况下消息消费者是自动确认消息的，如果要手动确认消息则需要修改确认模式为manual
        prefetch: 1 # 在prefetch=1的情况下，消费者在确认当前消息之前不会收到新的消息  在多个消费者下也不会将多个未确认的消息同时发送给同一个消费者。

  #Redis的配置
  data:
    redis:
      host: ***************
      port: 6379
      database: 0
      timeout: 1800000
      password: hzy123456
      jedis: 
        pool:
          max-active: 20 #最大连接数
          max-wait: -1    #最大阻塞等待时间(负数表示没限制)
          max-idle: 5    #最大空闲
          min-idle: 0     #最小空闲
    mongodb:
      host: ***************
      port: 27017
      database: tingshu #指定操作的数据库
  #MySQL的配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource #使用最快的数据库连接池
    driver-class-name: com.mysql.cj.jdbc.Driver #MySQL驱动
    url: *************************************************************************************************************************
    username: root
    password: root
