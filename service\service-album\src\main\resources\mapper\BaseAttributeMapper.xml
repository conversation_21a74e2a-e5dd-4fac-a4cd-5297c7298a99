<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.tingshu.album.mapper.BaseAttributeMapper">

	<resultMap id="baseAttributeAndValueMap" type="com.atguigu.tingshu.model.album.BaseAttribute" autoMapping="true">
		<id column="id" property="id"/>
		<collection property="attributeValueList" ofType="com.atguigu.tingshu.model.album.BaseAttributeValue" autoMapping="true">
			<id column="vId" property="id"/>
		</collection>
	</resultMap>

	<select id="findAttribute" resultMap="baseAttributeAndValueMap">
		SELECT
		    ba.id,
			ba.category1_Id category1Id,
			ba.attribute_name attributeName,

			bav.id vId,
			bav.attribute_id attributeId,
			bav.value_name valueName
		FROM `base_attribute` as ba
			     INNER JOIN `base_attribute_value` as bav
			                ON ba.id = bav.attribute_id
		WHERE ba.category1_id =#{c1Id} and ba.is_deleted = 0
	</select>
</mapper>

