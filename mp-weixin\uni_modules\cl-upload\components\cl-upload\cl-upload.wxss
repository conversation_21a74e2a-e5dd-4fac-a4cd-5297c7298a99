/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cl-updata .file-list.data-v-30f1beb2 {
  display: grid;
}
.cl-updata .file-list-row.data-v-30f1beb2 {
  display: inline-flex;
  align-items: center;
  position: relative;
}
.cl-updata .file-list-row image.data-v-30f1beb2 {
  height: 100%;
  width: 100%;
}
.cl-updata .file-list-row ._video.data-v-30f1beb2 {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.cl-updata .file-list-row ._video .video-fixed.data-v-30f1beb2 {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  z-index: 5;
}
.cl-updata .file-list-row ._video .play.data-v-30f1beb2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30%;
  z-index: 9;
}
.cl-updata .file-list-row .remove.data-v-30f1beb2 {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #373737;
  height: 50rpx;
  width: 50rpx;
  border-bottom-left-radius: 200rpx;
  z-index: 11;
}
.cl-updata .file-list-row .remove image.data-v-30f1beb2 {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  right: 12rpx;
  top: 12rpx;
}
.cl-updata .file-list .add-image.data-v-30f1beb2 {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ccc;
}
.cl-updata .file-list .add-image.data-v-30f1beb2:active {
  opacity: 0.8;
}
.cl-updata .file-list .add-image image.data-v-30f1beb2 {
  width: 40%;
}
.cl-updata .mask.data-v-30f1beb2 {
  background-color: #000;
  position: fixed;
  top: 0%;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
}
.cl-updata .mask .block.data-v-30f1beb2 {
  padding: 0 30rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}
.cl-updata .mask .block video.data-v-30f1beb2 {
  width: 100%;
  height: 78vh;
}
.cl-updata .mask ._root.data-v-30f1beb2 {
  width: 60rpx;
  height: 60rpx;
  position: absolute;
  left: 40rpx;
  top: 5vh;
}