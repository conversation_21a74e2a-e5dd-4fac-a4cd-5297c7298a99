package com.atguigu.tingshu.album.service.impl;

import com.atguigu.tingshu.album.service.FileUploadService;
import io.minio.*;
import io.minio.errors.MinioException;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {
    @Override
    public String fileUploadService(MultipartFile file) throws IOException, NoSuchAlgorithmException, InvalidKeyException {
        try {
            //1. 创建 minio客户端
            MinioClient minioClient =
                    MinioClient.builder()
                            .endpoint("http://***************:9000")
                            .credentials("admin", "admin123456")
                            .build();

            //2. 判断桶是否存在
            boolean found =
                    minioClient.bucketExists(BucketExistsArgs.builder().bucket("demo").build());
            if (!found) {
                //3. 创建桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket("demo").build());
            } else {
                System.out.println("桶已经存在");
            }

            //4. 上传文件到桶中

            PutObjectArgs.Builder putObjectArgsBuilder = PutObjectArgs.builder();
            PutObjectArgs putObjectArgs = putObjectArgsBuilder
                    .bucket("tingshu")
                    .object(file.getOriginalFilename())
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .build();
            ObjectWriteResponse objectWriteResponse = minioClient.putObject(putObjectArgs);
            //获取照片的url

            System.out.println("上传成功");
        } catch (MinioException e) {
            System.out.println("Error occurred: " + e.getMessage());
        }
        return null;
    }
}
