<view class="gui-bg-grey data-v-93207a4f"><z-paging wx:if="{{e}}" class="r data-v-93207a4f" u-s="{{['d']}}" u-r="zPagingRef" bindquery="{{c}}" u-i="93207a4f-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><view wx:for="{{a}}" wx:for-item="order" wx:key="f" class="gui-order gui-bg-white gui-dark-bg-level-3 data-v-93207a4f" bindtap="{{order.g}}"><view class="gui-flex gui-row gui-space-between gui-align-items-center gui-m-b-20 data-v-93207a4f"><text class="gui-order-number gui-primary-text data-v-93207a4f">订单号 : {{order.a}}</text></view><view wx:for="{{order.b}}" wx:for-item="shop" wx:key="e" class="data-v-93207a4f"><view class="gui-order-goods gui-flex data-v-93207a4f"><view class="gui-order-goods-img data-v-93207a4f"><gui-image wx:if="{{shop.b}}" class="data-v-93207a4f" u-i="{{shop.a}}" bind:__l="__l" u-p="{{shop.b}}"></gui-image></view><text class="gui-order-goods-name gui-secondary-text data-v-93207a4f">{{shop.c}}</text><text class="gui-order-goods-price data-v-93207a4f">￥{{shop.d}}</text></view></view><view class="gui-order-footer gui-flex gui-row gui-space-between gui-align-items-center data-v-93207a4f"><text class="gui-order-number data-v-93207a4f">{{order.c}} {{order.d}}</text><text class="gui-text-small gui-color-gray data-v-93207a4f">共1件商品，合计:￥{{order.e}}</text></view></view></z-paging></view>