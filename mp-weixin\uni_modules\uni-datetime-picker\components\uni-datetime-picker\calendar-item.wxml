<view class="{{['uni-calendar-item__weeks-box', j && 'uni-calendar-item--disable', k && 'uni-calendar-item--before-checked-x', l && 'uni-calendar-item--multiple', m && 'uni-calendar-item--after-checked-x']}}" bindtap="{{n}}" bindmouseenter="{{o}}"><view class="{{['uni-calendar-item__weeks-box-item', c && 'uni-calendar-item--checked', d && 'uni-calendar-item--checked-range-text', e && 'uni-calendar-item--before-checked', f && 'uni-calendar-item--multiple', g && 'uni-calendar-item--after-checked', h && 'uni-calendar-item--disable']}}"><text wx:if="{{a}}" class="uni-calendar-item__weeks-box-circle"></text><text class="uni-calendar-item__weeks-box-text uni-calendar-item__weeks-box-text-disable uni-calendar-item--checked-text">{{b}}</text></view><view class="{{[i && 'uni-calendar-item--today']}}"></view></view>