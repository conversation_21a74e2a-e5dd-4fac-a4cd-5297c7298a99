<view class="gui-load-more gui-flex gui-row gui-align-items-center gui-justify-content-center data-v-7ad38fb8" catchtap="{{i}}"><view wx:if="{{a}}" class="data-v-7ad38fb8"><text class="gui-block data-v-7ad38fb8" style="{{'height:' + b}}"></text></view><view wx:if="{{c}}" class="gui-load-more-icon data-v-7ad38fb8" ref="loadingiconforloadmore"><text class="{{[d, 'gui-icons', 'gui-rotate360', 'gui-block', 'data-v-7ad38fb8']}}" style="{{'font-size:' + e}}"></text></view><text class="{{['gui-block', 'data-v-7ad38fb8', g]}}" style="{{'font-size:' + h}}">{{f}}</text></view>