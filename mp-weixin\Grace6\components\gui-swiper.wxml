<view class="gui-swiper-card-wrap data-v-97684a3b"><swiper style="{{'width:' + c + ';' + ('height:' + d)}}" class="gui-swiper-card data-v-97684a3b" indicator-dots="{{false}}" interval="{{e}}" circular="{{true}}" autoplay="{{f}}" current="{{g}}" previous-margin="{{h}}" next-margin="{{i}}" bindchange="{{j}}"><swiper-item wx:for="{{a}}" wx:for-item="item" wx:key="S" class="gui-swiper-card-item gui-border-box data-v-97684a3b"><navigator wx:if="{{item.a}}" class="gui-swiper-card-nav gui-transition-all data-v-97684a3b" url="{{item.h}}" open-type="{{item.i}}" hover-class="none" style="{{'padding-left:' + item.j + ';' + ('padding-right:' + item.k) + ';' + ('padding-top:' + item.l) + ';' + ('padding-bottom:' + item.m)}}"><image style="{{'border-radius:' + item.b + ';' + ('width:' + item.c) + ';' + ('height:' + item.d) + ';' + ('opacity:' + item.e)}}" src="{{item.f}}" mode="{{item.g}}" class="gui-swiper-card-image gui-transition-all data-v-97684a3b"/></navigator><view wx:if="{{item.n}}" class="gui-swiper-card-nav gui-transition-all data-v-97684a3b" hover-class="none" catchtap="{{item.v}}" data-index="{{item.w}}" style="{{'padding-left:' + item.x + ';' + ('padding-right:' + item.y) + ';' + ('padding-top:' + item.z) + ';' + ('padding-bottom:' + item.A)}}"><image style="{{'border-radius:' + item.o + ';' + ('width:' + item.p) + ';' + ('height:' + item.q) + ';' + ('opacity:' + item.r)}}" src="{{item.s}}" mode="{{item.t}}" class="gui-swiper-card-image gui-transition-all data-v-97684a3b"/></view><view wx:if="{{b}}" class="{{['gui-indicator-dot-numbers', 'gui-flex', 'gui-row', 'gui-nowrap', 'data-v-97684a3b', item.L]}}" style="{{'height:' + item.M + ';' + ('border-bottom-left-radius:' + item.N) + ';' + ('border-bottom-right-radius:' + item.O) + ';' + ('width:' + item.P) + ';' + ('left:' + item.Q) + ';' + ('bottom:' + item.R)}}"><text class="gui-indicator-dot-text data-v-97684a3b" style="{{'padding-left:' + '20rpx' + ';' + ('font-style:' + 'italic') + ';' + ('color:' + item.C)}}">{{item.B}}</text><text class="gui-indicator-dot-text data-v-97684a3b" style="{{'font-size:' + '36rpx' + ';' + ('color:' + item.D)}}">/</text><text class="gui-indicator-dot-text data-v-97684a3b" style="{{'font-size:' + '28rpx' + ';' + ('padding-right:' + '20rpx') + ';' + ('font-style:' + 'italic') + ';' + ('color:' + item.F)}}">{{item.E}}</text><text class="gui-swiper-text gui-block-text gui-flex1 gui-ellipsis data-v-97684a3b" style="{{'color:' + item.H + ';' + ('font-size:' + item.I) + ';' + ('height:' + item.J) + ';' + ('line-height:' + item.K)}}">{{item.G}}</text></view></swiper-item></swiper><view wx:if="{{k}}" class="gui-indicator-dots gui-flex gui-row gui-nowrap gui-justify-content-center gui-align-items-center gui-border-box data-v-97684a3b" style="{{'width:' + o + ';' + ('height:' + p) + ';' + ('position:' + q) + ';' + ('padding-left:' + r) + ';' + ('padding-right:' + s) + ';' + ('justify-content:' + t)}}"><view class="gui-indicator-dots-wrap gui-flex gui-row gui-nowrap gui-justify-content-center data-v-97684a3b"><view wx:for="{{l}}" wx:for-item="item" wx:key="a" class="{{['data-v-97684a3b', 'gui-indicator-dot', item.b, item.c]}}" style="{{'width:' + item.d + ';' + ('height:' + m) + ';' + ('border-radius:' + n)}}"></view></view></view></view>