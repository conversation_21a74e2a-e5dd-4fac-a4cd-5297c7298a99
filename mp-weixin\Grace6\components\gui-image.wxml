<view class="gui-img gui-bg-gray gui-dark-bg-level-3 data-v-2a3c977b" style="{{'width:' + v + ';' + ('height:' + w) + ';' + ('border-radius:' + x)}}"><image class="data-v-2a3c977b" src="{{a}}" bindload="{{b}}" binderror="{{c}}" mode="{{d}}" style="{{'width:' + e + ';' + ('height:' + f) + ';' + ('border-radius:' + g) + ';' + ('opacity:' + h)}}"></image><text wx:if="{{i}}" class="{{['gui-img-loading', 'gui-icons', 'gui-color-gray', 'data-v-2a3c977b', j]}}" style="{{'width:' + k + ';' + ('height:' + l) + ';' + ('line-height:' + m) + ';' + ('border-radius:' + n)}}"></text><text wx:if="{{o}}" class="{{['gui-img-loading', 'gui-icons', 'gui-color-gray', 'data-v-2a3c977b', p]}}" style="{{'width:' + q + ';' + ('height:' + r) + ';' + ('line-height:' + s) + ';' + ('border-radius:' + t)}}"></text></view>