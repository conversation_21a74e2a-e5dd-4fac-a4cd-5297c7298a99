/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mianImgBg.data-v-eca06f3c {
  left: 0;
  top: 0;
  z-index: 1;
}
.mainScrollView.data-v-eca06f3c {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: 750rpx;
  border-radius: 8rpx;
}
.popupScrollView.data-v-eca06f3c {
  height: calc(100vh - var(--window-top) - 250px);
}
.popupView.data-v-eca06f3c {
  height: calc(100vh - var(--window-top) - 200px);
}
.gui-album-img.data-v-eca06f3c {
  width: 150rpx;
  height: 150rpx;
  border: 2rpx solid gold;
  border-radius: 10rpx;
}
.gui-album-avatar.data-v-eca06f3c {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.gui-text-brown.data-v-eca06f3c {
  color: #b3a598;
}
.gui-text-brown-light.data-v-eca06f3c {
  color: #e2bb92;
}

/*  深度样式 */
.guiSpread.data-v-eca06f3c .gui-editor-show {
  width: 690rpx !important;
}
.create-team-scroll.data-v-eca06f3c {
  white-space: nowrap;
  width: 100%;
  padding: 20rpx 20rpx 20rpx 0rpx;
}
.create-team-item.data-v-eca06f3c {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  border-radius: 20rpx;
}
.create-team-avatar.data-v-eca06f3c {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.buy-card.data-v-eca06f3c {
  width: 160rpx;
  text-align: center;
  margin: 10rpx;
}
.resizable-top.data-v-eca06f3c {
  height: 80rpx;
}
.track-item-checked.data-v-eca06f3c {
  background-color: #f5f5f5;
}
.track-item-title-checked.data-v-eca06f3c {
  color: #ff0036;
}
.track-item-sort.data-v-eca06f3c {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60rpx;
  text-align: center;
}
.price-container.data-v-eca06f3c {
  color: #ff6e40;
}
.buy-track-container.data-v-eca06f3c {
  justify-content: space-evenly;
}