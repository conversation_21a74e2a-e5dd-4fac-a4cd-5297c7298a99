<view class="gui-editor-show gui-border-box data-v-2ce5e431" style="{{'padding-left:' + c + ';' + ('padding-right:' + d) + ';' + ('width:' + '750rpx')}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="M" class="data-v-2ce5e431"><text wx:if="{{item.a}}" decode="{{true}}" selectable="{{true}}" user-select="{{true}}" class="{{['gui-block', 'data-v-2ce5e431', item.c]}}">{{item.b}}</text><text wx:if="{{item.d}}" class="{{['gui-block', 'gui-text-center', 'data-v-2ce5e431', item.f]}}" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.e}}</text><view wx:elif="{{item.g}}" class="gui-img-in data-v-2ce5e431" data-url="{{item.j}}" bindtap="{{item.k}}"><gui-image wx:if="{{item.i}}" class="data-v-2ce5e431" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"></gui-image></view><text wx:elif="{{item.l}}" class="{{['gui-block', 'data-v-2ce5e431', item.n]}}" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.m}}</text><text wx:elif="{{item.o}}" class="{{['gui-block', 'gui-bold', 'data-v-2ce5e431', item.q]}}" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.p}}</text><view wx:elif="{{item.r}}" class="data-v-2ce5e431"><gui-link wx:if="{{item.t}}" class="data-v-2ce5e431" u-i="{{item.s}}" bind:__l="__l" u-p="{{item.t}}"></gui-link></view><text wx:elif="{{item.v}}" class="{{['gui-block', 'gui-text-center', 'data-v-2ce5e431', item.w]}}" selectable="{{true}}" user-select="{{true}}">● ● ●</text><text wx:elif="{{item.x}}" class="gui-block gui-h1 gui-primary-text data-v-2ce5e431" decode="{{true}}" selectable="{{true}}" user-select="{{true}}">{{item.y}}</text><text wx:elif="{{item.z}}" class="gui-block gui-h2 gui-primary-text data-v-2ce5e431" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.A}}</text><text wx:elif="{{item.B}}" class="gui-block gui-h3 gui-primary-text data-v-2ce5e431" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.C}}</text><text wx:elif="{{item.D}}" class="gui-block gui-h4 gui-primary-text data-v-2ce5e431" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.E}}</text><text wx:elif="{{item.F}}" class="gui-block gui-h5 gui-primary-text data-v-2ce5e431" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.G}}</text><text wx:elif="{{item.H}}" class="gui-block gui-h6 gui-primary-text data-v-2ce5e431" selectable="{{true}}" user-select="{{true}}" decode="{{true}}">{{item.I}}</text><view wx:if="{{item.J}}" class="data-v-2ce5e431"><video class="data-v-2ce5e431" style="{{'width:' + item.K}}" src="{{item.L}}" controls></video></view><view class="data-v-2ce5e431" style="{{'height:' + b}}"></view></view></view>