<view class="le-dropdown data-v-835732cc" style="{{h}}"><view class="le-dropdown-menu data-v-835732cc"><view wx:for="{{a}}" wx:for-item="item" wx:key="k" class="le-dropdown-menu-item data-v-835732cc" catchtap="{{item.l}}"><view wx:if="{{item.a}}" class="le-flex data-v-835732cc"><text class="le-dropdown-menu-item-text data-v-835732cc" style="{{'color:' + item.c}}">{{item.b}}</text></view><view wx:else class="le-flex data-v-835732cc"><text class="le-dropdown-menu-item-text data-v-835732cc" style="{{'color:' + item.e}}">{{item.d}}</text><view wx:if="{{item.f}}" class="{{['data-v-835732cc', 'le-dropdown-menu-item-arrow', item.g, item.h]}}"></view><view wx:if="{{item.i}}" class="{{['data-v-835732cc', 'le-dropdown-menu-item-basicarrow', item.j]}}"></view></view></view></view><view class="le-dropdown-content data-v-835732cc" style="{{e + ';' + f}}" bindtap="{{g}}"><view ref="leDropdownContentPopupRef" class="le-dropdown-content-popup data-v-835732cc" style="{{c}}" catchtap="{{d}}"><block wx:for="{{b}}" wx:for-item="item" wx:key="n"><view wx:if="{{item.a}}" class="le-dropdown-popup-content le-dropdown-cell data-v-835732cc"><view wx:for="{{item.b}}" wx:for-item="sItem" wx:key="c" class="{{['data-v-835732cc', 'le-dropdown-cell-item', sItem.b]}}" bindtap="{{sItem.d}}">{{sItem.a}}</view></view><view wx:if="{{item.c}}" class="le-dropdown-popup-content le-dropdown-picker data-v-835732cc"><le-dropdown-picker wx:if="{{item.f}}" class="data-v-835732cc" u-i="{{item.d}}" bind:__l="__l" bindupdateModelValue="{{item.e}}" u-p="{{item.f}}"></le-dropdown-picker><view class="le-dropdown-footer data-v-835732cc"><view class="le-dropdown-confirm data-v-835732cc" bindtap="{{item.h}}">{{item.g}}</view></view></view><view wx:if="{{item.i}}" class="le-dropdown-popup-content le-dropdown-filter data-v-835732cc"><scroll-view class="data-v-835732cc" show-scrollbar="{{false}}" scroll-y="{{true}}" style="height:500rpx" scroll-top="{{100}}"><view wx:for="{{item.j}}" wx:for-item="sItem" wx:key="q" class="le-dropdown-filter-item data-v-835732cc"><view class="le-dropdown-filter-title data-v-835732cc">{{sItem.a}} <text wx:if="{{sItem.b}}" class="le-dropdown-filter-subtitle data-v-835732cc">{{sItem.c}}{{sItem.d}}</text></view><view class="le-dropdown-filter-content data-v-835732cc"><block wx:if="{{sItem.e}}"><view wx:for="{{sItem.f}}" wx:for-item="ssItem" wx:key="c" class="{{['data-v-835732cc', 'le-dropdown-filter-box', ssItem.b]}}" bindtap="{{ssItem.d}}">{{ssItem.a}}</view></block><block wx:elif="{{sItem.g}}"><view wx:for="{{sItem.h}}" wx:for-item="ssItem" wx:key="c" class="{{['data-v-835732cc', 'le-dropdown-filter-box', ssItem.b]}}" bindtap="{{ssItem.d}}">{{ssItem.a}}</view></block><block wx:elif="{{sItem.i}}"><slider class="data-v-835732cc" style="width:100%" activeColor="{{sItem.j}}" value="{{sItem.k}}" min="{{sItem.l}}" max="{{sItem.m}}" step="{{sItem.n}}" show-value="{{sItem.o}}" bindchange="{{sItem.p}}"/></block></view></view></scroll-view><view class="le-dropdown-footer data-v-835732cc"><view class="le-dropdown-reset data-v-835732cc" bindtap="{{item.k}}">重置</view><view class="le-dropdown-confirm data-v-835732cc" bindtap="{{item.m}}">{{item.l}}</view></view></view></block></view><view class="le-dropdown-content-mask data-v-835732cc"></view></view></view>