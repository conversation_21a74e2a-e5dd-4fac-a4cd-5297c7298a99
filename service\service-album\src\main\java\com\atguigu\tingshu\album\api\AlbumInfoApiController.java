package com.atguigu.tingshu.album.api;

import com.atguigu.tingshu.album.mapper.AlbumInfoMapper;
import com.atguigu.tingshu.album.service.AlbumInfoService;
import com.atguigu.tingshu.album.service.impl.AlbumInfoServiceImpl;
import com.atguigu.tingshu.common.login.annotation.TingShuLogin;
import com.atguigu.tingshu.common.result.Result;
import com.atguigu.tingshu.common.util.AuthContextHolder;
import com.atguigu.tingshu.model.album.AlbumInfo;
import com.atguigu.tingshu.query.album.AlbumInfoQuery;
import com.atguigu.tingshu.vo.album.AlbumInfoVo;
import com.atguigu.tingshu.vo.album.AlbumListVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "专辑管理")
@RestController
@RequestMapping("api/album/albumInfo")
@SuppressWarnings({"unchecked", "rawtypes"})
public class AlbumInfoApiController {

    @Autowired
    private AlbumInfoService albumInfoService;

    @Autowired
    private AlbumInfoServiceImpl albumInfoServiceImpl;

    @Autowired
    private AlbumInfoMapper albumInfoMapper;

    // Request URL: http://localhost:8500/api/album/albumInfo/saveAlbumInfo
    // (接)RequestBody: 将前端的json格式字符串 反序列化为javaBean对象
    // (返)ResponseBody: 将javaBean对象反序列化成json格式的字符串

    // 注意: javaBean前面的@RequestBody注解只有当前端在请求体中提交的数据是json格式的字符串, 才能加, 反之如果只是在请求体中
    // 提交普通key,value参数格式, 就不需要加@RequestBody注解
    @PostMapping("/saveAlbumInfo")
    @TingShuLogin
    @Operation(summary = "保存专辑信息")
    public Result saveAlbumInfo(@RequestBody AlbumInfoVo albumInfoVo) {

        albumInfoService.saveAlbumInfo(albumInfoVo);

        return Result.ok();
    }

    // Request URL: http://localhost:8500/api/album/albumInfo/findUserAlbumPage/1/10
    @PostMapping("/findUserAlbumPage/{pn}/{pz}")
    @TingShuLogin
    @Operation(summary = "分页查询用户专辑信息")
    public Result getUserAlbumByPage(
            @Parameter(name = "pageNum", description = "当前页码", required = true)
            @PathVariable Long pn,
            @Parameter(name = "pageSize", description = "每页记录数", required = true)
            @PathVariable Long pz,
            @Parameter(name = "albumInfoQuery", description = "查询对象", required = false)
            @RequestBody AlbumInfoQuery albumInfoQuery) {
        albumInfoQuery.setUserId(AuthContextHolder.getUserId());
        //1. 构建分页对象(对返回给前端的数据进行分页)
        IPage<AlbumListVo> pageParam = new Page<>(pn, pz);
        //2. 将分页对象传进去(未来自动给Page对象的records属性赋值: list)
        pageParam = albumInfoService.getUserAlbumByPage(pageParam, albumInfoQuery);
        //3. 将分页对象返回出去
        return Result.ok(pageParam);
    }

    // Request URL: http://localhost:8500/api/album/albumInfo/getAlbumInfo/1594
    @GetMapping("/getAlbumInfo/{albumId}")
    @TingShuLogin
    @Operation(summary = "根据id查询专辑信息")
    public Result getAlbumInfoById(@PathVariable(value = "albumId") Long albumId) {
        AlbumInfo albumInfoVo = albumInfoService.getAlbumInfo(albumId);
        return Result.ok(albumInfoVo);
    }

    // Request URL: http://localhost:8500/api/album/albumInfo/updateAlbumInfo/1594
    @PutMapping("/updateAlbumInfo/{albumId}")
    @TingShuLogin
    @Operation(summary = "根据id更新专辑信息")
    public Result updateAlbumInfo(@PathVariable(value = "albumId") Long albumId,
                                  @RequestBody AlbumInfoVo albumInfoVo) {
        albumInfoService.updateAlbumInfo(albumId, albumInfoVo);
        return Result.ok();
    }
}

