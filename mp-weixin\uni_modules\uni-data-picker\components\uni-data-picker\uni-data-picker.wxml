<view class="uni-data-tree"><view class="uni-data-tree-input" bindtap="{{n}}"><block wx:if="{{$slots.d}}"><slot name="d"></slot></block><block wx:else><view class="{{['input-value', l && 'input-value-border']}}"><text wx:if="{{a}}" class="selected-area error-text">{{b}}</text><view wx:elif="{{c}}" class="selected-area"><uni-load-more wx:if="{{d}}" class="load-more" u-i="67418ef5-0" bind:__l="__l" u-p="{{d}}"></uni-load-more></view><scroll-view wx:elif="{{e}}" class="selected-area" scroll-x="true"><view class="selected-list"><view wx:for="{{f}}" wx:for-item="item" wx:key="d" class="selected-item"><text class="text-color">{{item.a}}</text><text wx:if="{{item.b}}" class="input-split-line">{{item.c}}</text></view></view></scroll-view><text wx:else class="selected-area placeholder">{{g}}</text><view wx:if="{{h}}" class="icon-clear" catchtap="{{j}}"><uni-icons wx:if="{{i}}" u-i="67418ef5-1" bind:__l="__l" u-p="{{i}}"></uni-icons></view><view wx:if="{{k}}" class="arrow-area"><view class="input-arrow"></view></view></view></block></view><view wx:if="{{o}}" class="uni-data-tree-cover" bindtap="{{p}}"></view><view wx:if="{{q}}" class="uni-data-tree-dialog"><view class="uni-popper__arrow"></view><view class="dialog-caption"><view class="title-area"><text class="dialog-title">{{r}}</text></view><view class="dialog-close" bindtap="{{s}}"><view class="dialog-close-plus" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate" data-id="close"></view></view></view><data-picker-view wx:if="{{z}}" class="picker-view r" u-r="pickerView" bindchange="{{v}}" binddatachange="{{w}}" bindnodeclick="{{x}}" u-i="67418ef5-2" bind:__l="__l" bindupdateModelValue="{{y}}" u-p="{{z}}"></data-picker-view></view></view>