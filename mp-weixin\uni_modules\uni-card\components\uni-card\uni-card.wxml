<view class="{{['uni-card', p && 'uni-card--full', q && 'uni-card--shadow', r && 'uni-card--border']}}" style="{{'margin:' + s + ';' + ('padding:' + t) + ';' + ('box-shadow:' + v)}}"><block wx:if="{{$slots.cover}}"><slot name="cover"></slot></block><block wx:else><view wx:if="{{a}}" class="uni-card__cover"><image class="uni-card__cover-image" mode="widthFix" bindtap="{{b}}" src="{{c}}"></image></view></block><block wx:if="{{$slots.title}}"><slot name="title"></slot></block><block wx:else><view wx:if="{{d}}" class="uni-card__header"><view class="uni-card__header-box" bindtap="{{j}}"><view wx:if="{{e}}" class="uni-card__header-avatar"><image class="uni-card__header-avatar-image" src="{{f}}" mode="aspectFit"/></view><view class="uni-card__header-content"><text class="uni-card__header-content-title uni-ellipsis">{{g}}</text><text wx:if="{{h}}" class="uni-card__header-content-subtitle uni-ellipsis">{{i}}</text></view></view><view class="uni-card__header-extra" bindtap="{{l}}"><text class="uni-card__header-extra-text">{{k}}</text></view></view></block><view class="uni-card__content" style="{{'padding:' + m}}" bindtap="{{n}}"><slot></slot></view><view class="uni-card__actions" bindtap="{{o}}"><slot name="actions"></slot></view></view>