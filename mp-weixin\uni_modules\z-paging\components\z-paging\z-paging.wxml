<wxs src="./wxs/z-paging-wxs.wxs" module="pagingWxs"/>
<view class="{{['data-v-1aa372d7', 'z-paging-content', bh && 'z-paging-content-fixed', bi && 'z-paging-content-page', bj && 'z-paging-reached-top']}}" style="{{bk}}"><view wx:if="{{a}}" class="zp-safe-area-inset-bottom data-v-1aa372d7"></view><slot wx:if="{{b}}" name="top"/><view wx:elif="{{c}}" class="zp-page-top data-v-1aa372d7" style="{{d}}"><slot name="top"/></view><view class="{{['data-v-1aa372d7', 'zp-view-super', aV && 'zp-scroll-view-super']}}" style="{{aW}}"><view wx:if="{{e}}" class="{{['data-v-1aa372d7', 'zp-page-left', f && 'zp-absoulte']}}"><slot name="left"/></view><view class="{{['data-v-1aa372d7', 'zp-scroll-view-container', aR && 'zp-absoulte']}}" style="{{aS}}"><scroll-view ref="zp-scroll-view" class="{{['data-v-1aa372d7', 'zp-scroll-view', ax && 'zp-scroll-view-absolute', ay && 'zp-scroll-view-hide-scrollbar']}}" scroll-top="{{az}}" scroll-x="{{aA}}" scroll-y="{{aB}}" enable-back-to-top="{{aC}}" show-scrollbar="{{aD}}" scroll-with-animation="{{aE}}" scroll-into-view="{{aF}}" lower-threshold="{{aG}}" upper-threshold="{{5}}" refresher-enabled="{{aH}}" refresher-threshold="{{aI}}" refresher-default-style="{{aJ}}" refresher-background="{{aK}}" refresher-triggered="{{aL}}" bindscroll="{{aM}}" bindscrolltolower="{{aN}}" bindscrolltoupper="{{aO}}" bindrefresherrestore="{{aP}}" bindrefresherrefresh="{{aQ}}"><view class="zp-paging-touch-view data-v-1aa372d7" bindtouchstart="{{pagingWxs.touchstart}}" bindtouchmove="{{pagingWxs.touchmove}}" bindtouchend="{{pagingWxs.touchend}}" bindtouchcancel="{{pagingWxs.touchend}}" bindmousedown="{{pagingWxs.mousedown}}" bindmousemove="{{pagingWxs.mousemove}}" bindmouseup="{{pagingWxs.mouseup}}" bindmouseleave="{{pagingWxs.mouseleave}}"><view wx:if="{{g}}" class="zp-fixed-bac-view data-v-1aa372d7" style="{{h}}"></view><view class="zp-paging-main data-v-1aa372d7" style="{{ad + ';' + ae}}" change:prop="{{pagingWxs.propObserver}}" prop="{{af}}" data-refresherThreshold="{{ag}}" data-isIos="{{ah}}" data-loading="{{ai}}" data-useChatRecordMode="{{aj}}" data-refresherEnabled="{{ak}}" data-useCustomRefresher="{{al}}" data-pageScrollTop="{{am}}" data-scrollTop="{{an}}" data-refresherMaxAngle="{{ao}}" data-refresherAecc="{{ap}}" data-usePageScroll="{{aq}}" data-watchTouchDirectionChange="{{ar}}" data-oldIsTouchmoving="{{as}}" data-refresherOutRate="{{at}}" data-refresherPullRate="{{av}}" data-hasTouchmove="{{aw}}"><view wx:if="{{i}}" class="zp-custom-refresher-view data-v-1aa372d7" style="{{r}}"><view class="zp-custom-refresher-container data-v-1aa372d7" style="{{q}}"><view class="zp-custom-refresher-slot-view data-v-1aa372d7"><slot wx:if="{{j}}" name="refresher"/></view><slot wx:if="{{l}}" name="refresherComplete"/><z-paging-refresh wx:elif="{{m}}" class="r data-v-1aa372d7" u-r="refresh" style="{{o}}" u-i="1aa372d7-0" bind:__l="__l" u-p="{{p}}"/></view></view><view class="zp-paging-container data-v-1aa372d7"><slot wx:if="{{s}}" name="chatLoading"/><view wx:elif="{{t}}" class="zp-chat-record-loading-container data-v-1aa372d7"><text wx:if="{{v}}" bindtap="{{x}}" class="{{['data-v-1aa372d7', y]}}">{{w}}</text><image wx:else src="{{z}}" class="zp-chat-record-loading-custom-image data-v-1aa372d7"/></view><slot wx:if="{{A}}" name="loading"/><view class="zp-paging-container-content data-v-1aa372d7" style="{{T + ';' + U}}"><slot/><block wx:if="{{B}}"><slot name="header"/><view class="zp-list-container data-v-1aa372d7" style="{{H}}"><block wx:if="{{C}}"><view wx:for="{{D}}" wx:for-item="item" wx:key="d" class="zp-list-cell data-v-1aa372d7" style="{{F}}" id="{{item.c}}" bindtap="{{item.e}}"><view wx:if="{{E}}" class="data-v-1aa372d7">使用兼容模式请在组件源码z-paging.vue第99行中注释这一行，并打开下面一行注释</view><slot wx:else name="{{item.a}}"/></view></block><block wx:else><view wx:for="{{G}}" wx:for-item="item" wx:key="c" class="zp-list-cell data-v-1aa372d7" bindtap="{{item.d}}"><slot name="{{item.a}}"/></view></block></view><slot name="footer"/></block><view wx:if="{{I}}" class="zp-virtual-placeholder data-v-1aa372d7" style="{{J}}"/><slot wx:if="{{K}}" name="loadingMoreDefault"/><slot wx:elif="{{L}}" name="loadingMoreLoading"/><slot wx:elif="{{M}}" name="loadingMoreNoMore"/><slot wx:elif="{{N}}" name="loadingMoreFail"/><z-paging-load-more wx:elif="{{O}}" class="data-v-1aa372d7" binddoClick="{{P}}" u-i="1aa372d7-1" bind:__l="__l" u-p="{{Q}}"/><view wx:if="{{R}}" class="zp-safe-area-placeholder data-v-1aa372d7" style="{{S}}"/></view><view wx:if="{{V}}" class="{{['data-v-1aa372d7', 'zp-empty-view', ab && 'zp-empty-view-center']}}" style="{{ac}}"><slot wx:if="{{W}}" name="empty"/><z-paging-empty-view wx:else class="data-v-1aa372d7" bindreload="{{Y}}" bindviewClick="{{Z}}" u-i="1aa372d7-2" bind:__l="__l" u-p="{{aa||''}}"/></view></view></view></view></scroll-view></view><view wx:if="{{aT}}" class="{{['data-v-1aa372d7', 'zp-page-right', aU && 'zp-absoulte zp-right']}}"><slot name="right"/></view></view><slot wx:if="{{aX}}" name="bottom"/><view wx:elif="{{aY}}" class="zp-page-bottom data-v-1aa372d7" style="{{aZ}}"><slot name="bottom"/></view><view wx:if="{{ba}}" class="{{['data-v-1aa372d7', bd]}}" style="{{be}}" catchtap="{{bf}}"><slot wx:if="{{bb}}" name="backToTop"/><image wx:else class="zp-back-to-top-img data-v-1aa372d7" src="{{bc}}"/></view><view wx:if="{{bg}}" class="zp-loading-fixed data-v-1aa372d7"><slot name="loading"/></view></view>