<view class="gui-wrapper data-v-1d71fdc2"><view class="gui-menu-wrap data-v-1d71fdc2"><scroll-view scroll-y scroll-with-animation class="gui-tab-view menu-scroll-view data-v-1d71fdc2" scroll-top="{{b}}" scroll-into-view="{{c}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="b" class="{{['gui-tab-item', 'data-v-1d71fdc2', item.c]}}" catchtap="{{item.d}}"><text class="u-line-1 data-v-1d71fdc2">{{item.a}}</text></view></scroll-view><scroll-view scroll-top="{{e}}" scroll-y scroll-with-animation class="gui-right-box data-v-1d71fdc2" bindscroll="{{f}}"><view class="gui-page-view data-v-1d71fdc2"><view wx:for="{{d}}" wx:for-item="item" wx:key="d" class="gui-class-item data-v-1d71fdc2" id="{{item.c}}"><view class="gui-item-title data-v-1d71fdc2"><text class="data-v-1d71fdc2">{{item.a}}</text></view><view class="gui-item-container data-v-1d71fdc2"><view wx:for="{{item.b}}" wx:for-item="item1" wx:key="c" bindtap="{{item1.b}}" class="gui-thumb-box data-v-1d71fdc2"><view class="gui-item-menu-name data-v-1d71fdc2">{{item1.a}}</view></view></view><view class="data-v-1d71fdc2" style="height:100rpx;width:10px"></view></view></view></scroll-view></view></view>