<gui-page wx:if="{{i}}" u-s="{{['gHeader','gFixedTop','gBody','gFooter']}}" u-i="0981a8b7-0" bind:__l="__l" u-p="{{i}}"><view style="height:44px;background-color:#fff" class="gui-flex" slot="gHeader"><view class="gui-dark-bg-level-1 gui-p-15 gui-flex1" bindtap="{{b}}"><gui-search wx:if="{{a}}" u-i="0981a8b7-1,0981a8b7-0" bind:__l="__l" u-p="{{a}}"></gui-search></view><view style="width:200rpx"></view></view><view slot="gFixedTop"><view class="gui-flex gui-m-b-20"><view class="gui-bg-white gui-dark-bg-level-3 gui-p-r-20"><gui-switch-navigation wx:if="{{d}}" bindchange="{{c}}" u-i="0981a8b7-2,0981a8b7-0" bind:__l="__l" u-p="{{d}}"></gui-switch-navigation></view></view></view><view slot="gBody"><view style="height:88rpx"></view><gui-swiper wx:if="{{e}}" u-i="0981a8b7-3,0981a8b7-0" bind:__l="__l" u-p="{{e}}"></gui-swiper><secondary-classification-nav wx:if="{{f}}" u-i="0981a8b7-4,0981a8b7-0" bind:__l="__l" u-p="{{g}}"></secondary-classification-nav><goods-card wx:for="{{h}}" wx:for-item="item" wx:key="a" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></goods-card></view><bottom-nav u-i="0981a8b7-6,0981a8b7-0" bind:__l="__l" slot="gFooter"></bottom-nav></gui-page>