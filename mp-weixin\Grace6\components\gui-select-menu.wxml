<view class="gui-select-menu-wrap data-v-5b6f7438"><view wx:if="{{a}}" class="gui-masker data-v-5b6f7438" catchtap="{{b}}" catchtouchmove="{{c}}" style="{{'z-index:' + d}}"></view><view class="gui-select-menu-title gui-flex gui-rows gui-nowrap gui-justify-content-center gui-align-items-center data-v-5b6f7438" catchtap="{{k}}" id="menuMain"><text class="gui-block gui-ellipsis gui-primary-text data-v-5b6f7438" style="{{'font-size:' + f}}">{{e}}</text><text wx:if="{{g}}" style="{{'font-size:' + h}}" class="gui-icons gui-select-menu-title-icon gui-block gui-primary-text data-v-5b6f7438"></text><text wx:if="{{i}}" style="{{'font-size:' + j}}" class="gui-icons gui-select-menu-title-icon gui-block gui-primary-text data-v-5b6f7438"></text></view><view wx:if="{{l}}" class="gui-select-menu data-v-5b6f7438" style="{{'top:' + D + ';' + ('z-index:' + E)}}" catchtap="{{F}}" catchtouchmove="{{G}}"><view class="data-v-5b6f7438" style="margintop:90rpx;height:0px"></view><view wx:if="{{m}}" style="padding-bottom:10rpx" catchtap="{{x}}" class="gui-select-item gui-flex gui-rows gui-nowrap gui-align-items-center gui-bg-white gui-dark-bg-level-3 data-v-5b6f7438"><view class="gui-select-input gui-flex1 gui-bg-gray gui-dark-bg-level-1 data-v-5b6f7438"><input type="text" style="margin:15rpx" class="gui-form-input gui-flex1 gui-border-box data-v-5b6f7438" bindconfirm="{{n}}" bindinput="{{o}}" placeholder="{{p}}" value="{{q}}"/></view><text wx:if="{{r}}" class="gui-select-input-btn gui-block gui-primary-text data-v-5b6f7438" catchtap="{{t}}">{{s}}</text><text wx:elif="{{v}}" class="gui-select-input-btn gui-block gui-primary-text data-v-5b6f7438" catchtap="{{w}}">搜索</text></view><scroll-view scroll-y="{{true}}" show-scrollbar="{{false}}" style="{{'height:' + B + ';' + ('margin-top:' + '-2px')}}" class="gui-select-menus gui-border-box gui-bg-white gui-dark-bg-level-3 data-v-5b6f7438" scroll-into-view="{{C}}"><view wx:for="{{y}}" wx:for-item="item" wx:key="d" class="gui-select-item gui-flex gui-rows gui-nowrap gui-align-items-center data-v-5b6f7438" data-index="{{item.e}}" catchtap="{{item.f}}" style="{{'height:' + A}}" id="{{item.g}}"><text wx:if="{{item.a}}" class="gui-selected-icon gui-icons gui-primary-color data-v-5b6f7438" style="{{'font-size:' + item.b}}"></text><text class="gui-primary-text data-v-5b6f7438" style="{{'font-size:' + z}}">{{item.c}}</text></view><view style="height:25rpx" class="gui-bg-white gui-dark-bg-level-3 data-v-5b6f7438"></view></scroll-view></view></view>