<view class="container"><z-paging wx:if="{{f}}" class="r" u-s="{{['top','d']}}" u-r="zPagingRef" bindquery="{{d}}" u-i="d97fb85e-0" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><search-top u-i="d97fb85e-1,d97fb85e-0" bind:__l="__l" u-p="{{a}}" slot="top"></search-top><search-results-item wx:for="{{b}}" wx:for-item="item" wx:key="a" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></search-results-item></z-paging></view>