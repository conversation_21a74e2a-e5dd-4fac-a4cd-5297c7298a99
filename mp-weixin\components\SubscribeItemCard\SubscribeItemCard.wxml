<uni-card wx:if="{{j}}" class="data-v-9640d07a" u-s="{{['d']}}" u-i="9640d07a-0" bind:__l="__l" u-p="{{j}}"><view class="gui-list-items data-v-9640d07a"><view bindtap="{{b}}" class="gui-list-image gui-relative data-v-9640d07a"><image class="gui-list-image data-v-9640d07a" mode="aspectFill" src="{{a}}"></image></view><view bindtap="{{g}}" class="gui-list-body gui-border-b text-container data-v-9640d07a"><view class="gui-list-title data-v-9640d07a"><text class="gui-text gui-block gui-secondary-text gui-text-left gui-ellipsis data-v-9640d07a">{{c}}</text></view><text class="gui-list-body-desc gui-color-gray gui-ellipsis data-v-9640d07a">最后更新时间:{{d}}</text><gui-tags wx:if="{{e}}" class="data-v-9640d07a" u-i="9640d07a-1,9640d07a-0" bind:__l="__l" u-p="{{f}}"></gui-tags></view><uni-icons wx:if="{{i}}" bindclick="{{h}}" class="gui-m-r-10 data-v-9640d07a" u-i="9640d07a-2,9640d07a-0" bind:__l="__l" u-p="{{i}}"></uni-icons></view></uni-card><uni-popup wx:if="{{n}}" class="r data-v-9640d07a" u-s="{{['d']}}" u-r="cancelSubscribePopUpRef" u-i="9640d07a-3" bind:__l="__l" u-p="{{n}}"><uni-popup-dialog wx:if="{{l}}" class="data-v-9640d07a" bindconfirm="{{k}}" u-i="9640d07a-4,9640d07a-3" bind:__l="__l" u-p="{{l}}"></uni-popup-dialog></uni-popup>