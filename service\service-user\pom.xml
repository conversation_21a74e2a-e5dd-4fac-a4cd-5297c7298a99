<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>service</artifactId>
        <groupId>com.atguigu.tingshu</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service-user</artifactId>
    <packaging>jar</packaging>
    <version>1.0</version>

    <properties>
        <skipTests>true</skipTests>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>rabbit-util</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <!-- 微信小程序的sdk -->
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <!-- 引入jwt相关的依赖 -->
        <!-- <dependency> -->
	    <!--     <groupId>io.jsonwebtoken</groupId> -->
	    <!--     <artifactId>jjwt</artifactId> -->
	    <!--     <version>0.9.1</version> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
	    <!--     <groupId>javax.xml.bind</groupId> -->
        <!--     <artifactId>jaxb-api</artifactId> -->
        <!--     <version>2.3.0</version> -->
        <!-- </dependency> -->

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>4.3.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
