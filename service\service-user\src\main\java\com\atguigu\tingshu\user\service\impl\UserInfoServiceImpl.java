package com.atguigu.tingshu.user.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.atguigu.tingshu.common.constant.PublicConstant;
import com.atguigu.tingshu.common.constant.RedisConstant;
import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.common.rabbit.constant.MqConst;
import com.atguigu.tingshu.common.rabbit.service.RabbitService;
import com.atguigu.tingshu.common.util.AuthContextHolder;
import com.atguigu.tingshu.model.user.UserInfo;
import com.atguigu.tingshu.user.mapper.UserInfoMapper;
import com.atguigu.tingshu.user.service.UserInfoService;
import com.atguigu.tingshu.vo.user.UserInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.jwt.Jwt;
import org.springframework.security.jwt.JwtHelper;
import org.springframework.security.jwt.crypto.sign.RsaSigner;
import org.springframework.security.jwt.crypto.sign.RsaVerifier;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

	@Autowired
	private UserInfoMapper userInfoMapper;

	@Autowired
	private WxMaService wxMaService;

    @Autowired
    private RsaSigner rsaSigner;

	@Autowired
	private StringRedisTemplate redisTemplate;

	@Autowired
	private RabbitService rabbitService;

	/**
	 * 普通的token和jwt生成的token的区别:
	 * 1. 普通的token没有任何代表习性 普通的token不安全
	 * 2. jwt有代表性(载荷中自定义信息)
	 * @param code
	 * @return
	 */
	@Override
	public Map<String, Object> wxLogin(String code) {

		//1. 判断code码是否存在
		if (StringUtils.isEmpty(code)) {
			throw new GuiguException(201, "code不存在");
        }

		String openid = "";

		//2. 调用微信服务端
		WxMaUserService userService = wxMaService.getUserService();
        WxMaJscode2SessionResult sessionInfo = null;
        try {
            sessionInfo = userService.getSessionInfo(code);
			openid = sessionInfo.getOpenid();
        } catch (WxErrorException e) {
			log.error("调用微信服务端失败{}", e.getMessage());
			throw new GuiguException(201, "调用微信服务端失败");
        }

		String refreshTokenKey = RedisConstant.USER_LOGIN_REFRESH_KEY_PREFIX + openid;

		String jsonWebTokenFromRedis = redisTemplate.opsForValue().get(refreshTokenKey);
		if (!StringUtils.isEmpty(jsonWebTokenFromRedis)) {
			HashMap<String, Object> map = new HashMap<>();
			map.put("token", jsonWebTokenFromRedis);
			return map;
		}

		//3. 根据openid查询用户信息
		LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(UserInfo::getWxOpenId, openid);
        UserInfo userInfo = userInfoMapper.selectOne(queryWrapper);

		if (userInfo == null) {
			//TODO
			//1. 向user_info表中插入用户(注册用户信息)
			userInfo = new UserInfo();
			userInfo.setWxOpenId(openid);
			userInfo.setNickname("WX_" + openid.substring(0, 6));
			userInfo.setAvatarUrl("https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg");
			userInfo.setIsVip(0);
			int insert = userInfoMapper.insert(userInfo);
			log.info("注册用户,{}", insert > 0 ? "success" : "failed");
			//2. TODO
			//   像tingshu_account库中的user_account表中插入用户账户(初始化用户账户余额)
			/**
			 * param1: 交换机
			 * param2: 路由键
			 * param3: 消息内容
			 */
			//只要涉及网络传输, 使用字符串永远是最稳最安全的 toString()
			rabbitService.sendMessage(MqConst.EXCHANGE_USER,MqConst.ROUTING_USER_REGISTER,userInfo.getId().toString());
			log.info("用户微服务发送初始化用户账户余额消息: {}成功", userInfo.getId());
		}

		Map<String, Object> map = new HashMap<>();

		//传统方式
//		String token = UUID.randomUUID().toString().replace("-", "");
//		token = token + openid + userInfo.getId(); // token是一个jwt生成的令牌

		//RSA: 非对称加密方式(公钥(加密和验签) 和 私钥(解密 和 加签:防止数据被篡改)) 对称加密(一把钥匙)
		//加签: 为了防止数据被篡改
		//加密: 将数据转成密文, 对敏感数据做保护的

		//定义一个载荷
		//载荷中可以放入一些自定义的信息, 例如用户id, openid等
		//4. 生成一个token值返回给前端
		String token = getJsonWebToken(userInfo.getId().toString(), openid);
		//存放在服务端的内存中(Session) MySQL(一个微服务 各个微服务都能取到 比较慢) Redis(一个微服务 各个微服务都能取到 比较快)
		map.put("token", token); // token值

		//Redis常用数据类型有5种: String, Hash, List, Set, ZSet
		//将jsonWebToken存入到Redis中
		String accessTokenKey = RedisConstant.USER_LOGIN_KEY_PREFIX + openid;

		//给用户登录的信息设置过期时间
		//长 短
		//短: 频繁登录      优点: 安全性高(攻击的时间窗口较小)
		//长: 不用频繁登录   缺点: 不太安全(攻击的时间窗口较大)

		//引入一个token(1.为了提高用户的体验 2.安全)

		//双token机制 accessToken(时间短, 一般在分钟或者小时级别) 和 refreshToken(时间长, 一般在几天或者月 级别)

		redisTemplate.opsForValue().set(accessTokenKey, token, 10, TimeUnit.DAYS); // 10分钟(短一点)
		redisTemplate.opsForValue().set(refreshTokenKey, token, 180, TimeUnit.DAYS); // 10天(长一点)

		//6. 返回
		return map;
	}

	@Override
	public Map<String, Object> getNewAccessToken() {

		HashMap<String, Object> result = new HashMap<>();

		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		//1.2 获取请求对象
		HttpServletRequest request = requestAttributes.getRequest();
		//1.3 获取请求对象的请求头
		String token = request.getHeader("token");
		if (StringUtils.isEmpty(token)) {
			throw new GuiguException(201, "之前没有登陆过");
		}

		//2. 校验jsonWebToken是否被篡改
		Jwt jwt = JwtHelper.decodeAndVerify(token, new RsaVerifier(PublicConstant.PUBLIC_KEY));

		//3. 校验通过 获取载荷数据
		String claims = jwt.getClaims();

		@SuppressWarnings("unchecked")
		Map<String, Object> map = JSONObject.parseObject(claims, Map.class);
		String openId = String.valueOf(map.get("openId"));
		String userId = String.valueOf(map.get("userId"));

		String refreshTokenKey = RedisConstant.USER_LOGIN_REFRESH_KEY_PREFIX + openId;
		String accessTokenKey = RedisConstant.USER_LOGIN_KEY_PREFIX + openId;
		//1. 从Redis中获取RefreshToken
		String refreshToken = redisTemplate.opsForValue().get(refreshTokenKey);
		//2. 判断RefreshToken是否存在
		if (!StringUtils.isEmpty(refreshToken)) {
			//2.2 如果有, 生成一个新令牌返回给前端
			String jsonWebToken = getJsonWebToken(String.valueOf(userId.toString()), openId);
			redisTemplate.opsForValue().set(accessTokenKey, token, 10, TimeUnit.DAYS); // 10分钟(短一点)
			redisTemplate.opsForValue().set(refreshTokenKey, token, 180, TimeUnit.DAYS); // 10天(长一点)
			result.put("token", jsonWebToken);
		} else {
			//2.1 如果没有, refreshToken过期, 需要重新登录
			result.put("1", "v");
		}
		return result;
	}

	@Override
	public void updateUser(UserInfoVo userInfoVo) {

		//1. 查询用户信息
		Long userId = AuthContextHolder.getUserId();
		UserInfo userInfo = userInfoMapper.selectById(userId);
		if (null == userInfo) {
			throw new GuiguException(201, "用户信息不存在");
		}
		userInfo.setNickname(userInfoVo.getNickname());
		userInfo.setAvatarUrl(userInfoVo.getAvatarUrl());
		userInfoMapper.updateById(userInfo);
	}

	private String getJsonWebToken(String userId, String openid) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("id", userId);
		jsonObject.put("openId", openid);
		//jwt方式生成
		Jwt jwt = JwtHelper.encode(jsonObject.toString(), rsaSigner);
		return jwt.getEncoded();
	}
}
