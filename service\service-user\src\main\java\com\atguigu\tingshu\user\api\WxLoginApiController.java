package com.atguigu.tingshu.user.api;

import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.common.login.annotation.TingShuLogin;
import com.atguigu.tingshu.common.result.Result;
import com.atguigu.tingshu.common.result.ResultCodeEnum;
import com.atguigu.tingshu.common.util.AuthContextHolder;
import com.atguigu.tingshu.model.user.UserInfo;
import com.atguigu.tingshu.user.service.UserInfoService;
import com.atguigu.tingshu.vo.user.UserInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Tag(name = "微信授权登录接口")
@RestController
@RequestMapping("/api/user/wxLogin")
@Slf4j
public class WxLoginApiController {

    @Autowired
    private UserInfoService userInfoService;

    //Request URL: http://localhost:8500/api/user/wxLogin/wxLogin/0d1zts000rNKHU1Q17100lXYfW3zts0s

    @GetMapping(value = "/wxLogin/{code}")
    @Operation(summary = "微信小程序登录")
    public Result wxLogin(@PathVariable(value = "code") String code) {

        // (前端要的)左边 == 右边(实现的功能)
        Map<String, Object> map =  userInfoService.wxLogin(code);
        return Result.ok(map);
    }

    //Request URL: http://localhost:8501/api/user/wxLogin/getUserInfo
    @GetMapping(value = "/getUserInfo")
    @Operation(summary = "获取用户信息")
    @TingShuLogin
    public Result getUserInfo() {
        //从ThreadLocal中获取userId
        Long userId = AuthContextHolder.getUserId();
        UserInfo userInfo = userInfoService.getById(userId);
        if (userInfo == null) {
            throw new GuiguException(201, "用户信息不存在");
        }
        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userInfo, userInfoVo);
        return Result.ok(userInfoVo);
    }

    @GetMapping(value = "/refreshToken/getNewAccessToken")
    @Operation(summary = "获取新令牌")
    @TingShuLogin
    public Result getNewAccessToken() {

        Map<String, Object> map = userInfoService.getNewAccessToken();
        if (!map.isEmpty() && map.containsKey("1")) {
            return Result.build(null, ResultCodeEnum.LOGIN_AUTH);
        }
        return Result.ok(map);
    }

    // Request URL: http://localhost:8500/api/user/wxLogin/updateUser
    @PostMapping("/updateUser")
    @Operation(summary = "更新用户信息")
    @TingShuLogin
    public Result updateUser(@RequestBody UserInfoVo userInfoVo) {
        userInfoService.updateUser(userInfoVo);
        return Result.ok();
    }

}