server:
  port: 8501
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 查看日志
spring:
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  cloud:
    openfeign:
      lazy-attributes-resolution: true
      client:
        config:
          default:
            connectTimeout: 30000
            readTimeout: 30000
            loggerLevel: basic
  rabbitmq:
    host: ***************
    port: 5672
    username: guest
    password: guest
    publisher-confirm-type: CORRELATED  # 发布确认模式
    publisher-returns: true  # 确保消息返回
    listener:
      simple:
        acknowledge-mode: manual #默认情况下消息消费者是自动确认消息的，如果要手动确认消息则需要修改确认模式为manual
        prefetch: 1 # 在prefetch=1的情况下，消费者在确认当前消息之前不会收到新的消息  在多个消费者下也不会将多个未确认的消息同时发送给同一个消费者。
  data:
    redis:
      host: ***************
      port: 6379
      database: 0
      timeout: 1800000
      password:
      jedis:
        pool:
          max-active: 20 #最大连接数
          max-wait: -1    #最大阻塞等待时间(负数表示没限制)
          max-idle: 5    #最大空闲
          min-idle: 0     #最小空闲
    mongodb:
      host: ***************
      port: 27017
      database: tingshu #指定操作的数据库
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: shangguigu@QY123
    hikari:
      connection-test-query: SELECT 1
      connection-timeout: 60000
      idle-timeout: 500000
      max-lifetime: 540000
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: GuliHikariPool
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
wechat:
  login:
    #小程序授权登录
    appId: wxa01763cf2a62e9a2  # 小程序微信公众平台appId
    appSecret: d9998e59aa9195a01ec17657355c1bde  # 小程序微信公众平台api秘钥
