package com.atguigu.tingshu.album.service;

import com.atguigu.tingshu.model.album.AlbumInfo;
import com.atguigu.tingshu.query.album.AlbumInfoQuery;
import com.atguigu.tingshu.vo.album.AlbumInfoVo;
import com.atguigu.tingshu.vo.album.AlbumListVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AlbumInfoService extends IService<AlbumInfo> {


    void saveAlbumInfo(AlbumInfoVo albumInfoVo);

    IPage<AlbumListVo> getUserAlbumByPage(IPage<AlbumListVo> pageParam, AlbumInfoQuery albumInfoQuery);

    AlbumInfo getAlbumInfo(Long albumId);

    void updateAlbumInfo(Long albumId, AlbumInfoVo albumInfoVo);
}
