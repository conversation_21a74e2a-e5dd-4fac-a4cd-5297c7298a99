<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>tingshu_parent241028</artifactId>
        <groupId>com.atguigu.tingshu</groupId>
        <version>1.0</version>
    </parent>

    <modules>
        <module>service-album-client</module>
        <module>service-user-client</module>
        <module>service-account-client</module>
        <module>service-order-client</module>
        <module>service-search-client</module>
    </modules>

    <artifactId>service-client</artifactId>
    <packaging>pom</packaging>
    <version>1.0</version>
    <description>应用的所有远程调用层</description>

    <dependencies>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>common-util</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.atguigu.tingshu</groupId>
            <artifactId>model</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 服务调用feign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
