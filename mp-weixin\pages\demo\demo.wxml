<view class="content data-v-d10efb47"><z-paging wx:if="{{j}}" class="r data-v-d10efb47" u-s="{{['top','d']}}" u-r="zPagingRef" bindquery="{{h}}" u-i="d10efb47-0" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"><view class="gui-dark-bg-level-3 gui-padding nav-top-container data-v-d10efb47" slot="top"><view class=" data-v-d10efb47"><gui-switch-navigation wx:if="{{b}}" class="data-v-d10efb47" bindchange="{{a}}" u-i="d10efb47-1,d10efb47-0" bind:__l="__l" u-p="{{b}}"></gui-switch-navigation></view><view class="select-container data-v-d10efb47"><gui-select-menu wx:if="{{d}}" class="data-v-d10efb47" bindselect="{{c}}" u-i="d10efb47-2,d10efb47-0" bind:__l="__l" u-p="{{d}}"></gui-select-menu><view class="gui-text-small select-add data-v-d10efb47"><text class="gui-icons gui-block gui-color-gray gui-text data-v-d10efb47"></text></view></view></view><block wx:if="{{e}}"><album-item-card wx:for="{{f}}" wx:for-item="item" wx:key="a" class="data-v-d10efb47" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></album-item-card></block></z-paging></view>