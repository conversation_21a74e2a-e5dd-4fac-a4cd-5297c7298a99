<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.tingshu.album.mapper.BaseCategory1Mapper">


	<resultMap id="baseCategoryResult" type="com.atguigu.tingshu.model.category.CategoryVo">
		<result property="category1Id" column="category1_id"/>
		<result property="category1Name" column="category1_name"/>
		<collection property="categoryChild" javaType="java.util.List" ofType="com.atguigu.tingshu.model.category.CategoryVo">
			<result property="category2Id" column="category2_id"/>
			<result property="category2Name" column="category2_name"/>
			<collection property="categoryChild" javaType="java.util.List" ofType="com.atguigu.tingshu.model.category.CategoryVo">
				<result property="category3Id" column="category3_id"/>
				<result property="category3Name" column="category3_name"/>
			</collection>
		</collection>

	</resultMap>
	
	<select id="getBaseCategoryList" resultMap="baseCategoryResult">
		select category1_id   category1Id,
		       category1_name category1Name,
		       category2_id   category2Id,
		       category2_name category2Name,
		       category3_id   category3Id,
		       category3_name category3Name
		from base_category_view
		where base_category_view.is_deleted=0
	</select>
</mapper>

