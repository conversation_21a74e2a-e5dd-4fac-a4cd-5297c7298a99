<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.atguigu.tingshu.album.mapper.BaseCategory1Mapper">


	<resultMap id="baseCategoryResult" type="com.atguigu.tingshu.model.category.CategoryVo">
		<result property="categoryId" column="category1Id"/>
		<result property="categoryName" column="category1Name"/>
		<collection property="categoryChild" ofType="com.atguigu.tingshu.model.category.CategoryVo">
			<result property="categoryId" column="category2Id"/>
			<result property="categoryName" column="category2Name"/>
			<collection property="categoryChild" ofType="com.atguigu.tingshu.model.category.CategoryVo">
				<result property="categoryId" column="category3Id"/>
				<result property="categoryName" column="category3Name"/>
			</collection>
		</collection>
	</resultMap>
	
	<select id="getBaseCategoryList" resultMap="baseCategoryResult">
		select category1_id   category1Id,
		       category1_name category1Name,
		       category2_id   category2Id,
		       category2_name category2Name,
		       category3_id   category3Id,
		       category3_name category3Name
		from base_category_view
		where base_category_view.is_deleted=0
	</select>
</mapper>

