/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup-share {
  background-color: #fff;
  border-top-left-radius: 11px;
  border-top-right-radius: 11px;
}
.uni-share-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 40px;
}
.uni-share-title-text {
  font-size: 14px;
  color: #666;
}
.uni-share-content {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 10px;
}
.uni-share-content-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 360px;
}
.uni-share-content-item {
  width: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px 0;
  align-items: center;
}
.uni-share-content-item:active {
  background-color: #f5f5f5;
}
.uni-share-image {
  width: 30px;
  height: 30px;
}
.uni-share-text {
  margin-top: 10px;
  font-size: 14px;
  color: #3B4144;
}
.uni-share-button-box {
  display: flex;
  flex-direction: row;
  padding: 10px 15px;
}
.uni-share-button {
  flex: 1;
  border-radius: 50px;
  color: #666;
  font-size: 16px;
}
.uni-share-button::after {
  border-radius: 50px;
}