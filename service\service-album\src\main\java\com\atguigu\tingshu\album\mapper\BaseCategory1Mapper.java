package com.atguigu.tingshu.album.mapper;

import com.atguigu.tingshu.model.album.BaseCategory1;
import com.atguigu.tingshu.model.category.CategoryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BaseCategory1Mapper extends BaseMapper<BaseCategory1> {


    /**
     * 查询分类 MyBatis自动封装
     * @return
     */
    List<CategoryVo> getBaseCategoryList();
}
