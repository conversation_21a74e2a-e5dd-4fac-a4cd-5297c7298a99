<view class="uni-stat__select"><label wx:if="{{a}}" class="uni-label-text hide-on-phone">{{b}}</label><view class="{{['uni-stat-box', r && 'uni-stat__actived']}}"><view class="{{['uni-select', q && 'uni-select--disabled']}}"><view class="uni-select__input-box" bindtap="{{j}}"><view wx:if="{{c}}" class="uni-select__input-text">{{d}}</view><view wx:else class="uni-select__input-text uni-select__input-placeholder">{{e}}</view><view wx:if="{{f}}" catchtap="{{h}}"><uni-icons wx:if="{{g}}" u-i="d85940ba-0" bind:__l="__l" u-p="{{g}}"/></view><view wx:else><uni-icons wx:if="{{i}}" u-i="d85940ba-1" bind:__l="__l" u-p="{{i}}"/></view></view><view wx:if="{{k}}" class="uni-select--mask" bindtap="{{l}}"/><view wx:if="{{m}}" class="uni-select__selector"><view class="uni-popper__arrow"></view><scroll-view scroll-y="true" class="uni-select__selector-scroll"><view wx:if="{{n}}" class="uni-select__selector-empty"><text>{{o}}</text></view><block wx:else><view wx:for="{{p}}" wx:for-item="item" wx:key="c" class="uni-select__selector-item" bindtap="{{item.d}}"><text class="{{[item.b && 'uni-select__selector__disabled']}}">{{item.a}}</text></view></block></scroll-view></view></view></view></view>