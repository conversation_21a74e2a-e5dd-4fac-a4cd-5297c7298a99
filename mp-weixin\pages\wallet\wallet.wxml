<gui-page u-s="{{['gBody']}}" u-i="50b9b29e-0" bind:__l="__l"><view slot="gBody"><view class="gui-bg-yellow gui-m-30 gui-p-30 gui-border-radius"><text>账户余额</text><view class="gui-flex gui-space-between gui-align-items-center"><view><text>￥</text><text class="gui-h1">{{a}}</text></view><view bindtap="{{b}}" class="gui-bg-white gui-p-30 gui-border-radius"><text class="gui-color-orange gui-bold">充 值</text></view></view></view><view class="gui-m-30"><navigator url="/pages/fund/fund?id=consume" class="gui-list-items"><view class="gui-list-body gui-border-b"><view class="gui-list-title"><text class="gui-list-title-text gui-list-one-line gui-primary-text">消费记录</text></view></view><text class="gui-list-arrow-right gui-icons gui-color-gray-light"></text></navigator><navigator url="/pages/fund/fund?id=invest" class="gui-list-items"><view class="gui-list-body gui-border-b"><view class="gui-list-title"><text class="gui-list-title-text gui-list-one-line gui-primary-text">充值记录</text></view></view><text class="gui-list-arrow-right gui-icons gui-color-gray-light"></text></navigator></view></view></gui-page><gui-popup wx:if="{{g}}" class="r" u-s="{{['d']}}" u-r="investPopupRef" u-i="50b9b29e-1" bind:__l="__l" u-p="{{g}}"><view class="gui-relative gui-box-shadow gui-bg-white gui-dark-bg-level-1"><text class="gui-icons gui-block gui-absolute-rt gui-h3 gui-p-20" bindtap="{{c}}"></text><text class="gui-h3 gui-block gui-p-t-20 gui-p-b-20 gui-text-center">充值</text><view class="gui-flex gui-padding gui-wrap gui-row buy-track-container"><view wx:for="{{d}}" wx:for-item="item" wx:key="b" bindtap="{{item.c}}" class="buy-card gui-text-small gui-flex gui-column gui-align-items-center gui-p-20 gui-border-radius gui-border"><text class="gui-text-orange-opacity9 gui-block gui-padding gui-h6">{{item.a}}</text></view><view bindtap="{{e}}" class="buy-card gui-text-small gui-flex gui-column gui-align-items-center gui-p-20 gui-border-radius gui-border"><text class="gui-block gui-padding gui-h6">自定义</text></view></view></view></gui-popup><uni-popup wx:if="{{l}}" class="r" u-s="{{['d']}}" u-r="customAmountPopupRef" u-i="50b9b29e-2" bind:__l="__l" u-p="{{l}}"><uni-popup-dialog wx:if="{{j}}" class="r" u-r="customAmountDialogRef" bindconfirm="{{i}}" u-i="50b9b29e-3,50b9b29e-2" bind:__l="__l" u-p="{{j}}"></uni-popup-dialog></uni-popup>