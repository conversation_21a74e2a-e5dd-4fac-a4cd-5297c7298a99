package com.atguigu.tingshu.album.service.impl;
import java.util.ArrayList;
import java.util.Date;

import com.atguigu.tingshu.album.mapper.AlbumAttributeValueMapper;
import com.atguigu.tingshu.album.mapper.AlbumStatMapper;
import com.atguigu.tingshu.album.service.AlbumAttributeValueService;
import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.model.album.AlbumStat;
import com.atguigu.tingshu.query.album.AlbumInfoQuery;
import com.atguigu.tingshu.vo.album.AlbumListVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;

import com.atguigu.tingshu.album.mapper.AlbumInfoMapper;
import com.atguigu.tingshu.album.service.AlbumInfoService;
import com.atguigu.tingshu.common.constant.SystemConstant;
import com.atguigu.tingshu.common.util.AuthContextHolder;
import com.atguigu.tingshu.model.album.AlbumAttributeValue;
import com.atguigu.tingshu.model.album.AlbumInfo;
import com.atguigu.tingshu.vo.album.AlbumAttributeValueVo;
import com.atguigu.tingshu.vo.album.AlbumInfoVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopConfigException;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class AlbumInfoServiceImpl extends ServiceImpl<AlbumInfoMapper, AlbumInfo> implements AlbumInfoService {

	@Autowired
	private AlbumInfoMapper albumInfoMapper;

	@Autowired
	private AlbumAttributeValueMapper albumAttributeValueMapper;

	@Autowired
	private AlbumStatMapper albumStatMapper;

	@Autowired
	private AlbumAttributeValueService albumAttributeValueService;

	@Override
	@Transactional(rollbackFor = Exception.class) //开启事务
	public void saveAlbumInfo(AlbumInfoVo albumInfoVo) {
		//入库 tingshu_album 表 (album_info album_attribute_value)
		Long userId = AuthContextHolder.getUserId();
		AlbumInfoServiceImpl proxyObject = (AlbumInfoServiceImpl)AopContext.currentProxy();

		//1. 保存专辑基本信息(album_info表中插入数据)
		//实体类专门用来和数据库列做映射
		AlbumInfo albumInfo = new AlbumInfo();

		BeanUtils.copyProperties(albumInfoVo, albumInfo);
		albumInfo.setUserId(userId); //额外赋值一次 (userId)
		albumInfo.setStatus(SystemConstant.ALBUM_STATUS_PASS); // 默认审核通过(TODO 对接后续系统)

		//获取专辑的付费类型(0101(免费)、0102(vip免费)、0103(付费))
		String payType = albumInfoVo.getPayType();
		if (!SystemConstant.ALBUM_PAY_TYPE_FREE.equals(payType)) {
			//默认给这个专辑设置5集免费声音
			albumInfo.setTracksForFree(5);
		}
		int insert = albumInfoMapper.insert(albumInfo);
		log.info("保存专辑信息: {}", insert > 0 ? "成功" : "失败");

		//2. 保存专辑的标签信息(album_attribute_value表中插入数据)
		List<AlbumAttributeValueVo> albumAttributeValueVoList = albumInfoVo.getAlbumAttributeValueVoList(); //获取选择的标签信息

		List<AlbumAttributeValue> attributeValues = albumAttributeValueVoList.stream().map(AlbumAttributeValueVo -> {
			AlbumAttributeValue albumAttributeValue = new AlbumAttributeValue();
			albumAttributeValue.setAlbumId(albumInfo.getId());
			albumAttributeValue.setAttributeId(AlbumAttributeValueVo.getAttributeId()); // 专辑的属性ID
			albumAttributeValue.setValueId(AlbumAttributeValueVo.getValueId()); //属性值Id
			return albumAttributeValue;
		}).collect(Collectors.toList());

		if (!CollectionUtils.isEmpty(attributeValues)) {
			boolean b = albumAttributeValueService.saveBatch(attributeValues);
			log.info("保存专辑的标签信息: {}", b ? "成功" : "失败");
		}

		//3. 保存专辑的统计信息(album_stat表中插入数据)
		// saveAlbumStat(albumInfo.getId());

		proxyObject.saveAlbumStat(albumInfo.getId());
	}

	@Override
	public IPage<AlbumListVo> getUserAlbumByPage(IPage<AlbumListVo> pageParam, AlbumInfoQuery albumInfoQuery) {
		return albumInfoMapper.getUserAlbumByPage(pageParam, albumInfoQuery);
	}

	@Override
	public AlbumInfo getAlbumInfo(Long albumId) {
		//1. 根据专辑id查询专辑基本信息
		AlbumInfo albumInfo = getById(albumId);
		if (albumInfo == null) {
			throw new GuiguException(201, "该专辑不存在");
		}
		AlbumAttributeValueMapper baseMapper1 = (AlbumAttributeValueMapper) albumAttributeValueService.getBaseMapper();
		//2. 查询专辑的标签信息(属性id和属性值id)
		LambdaQueryWrapper wrapper = new LambdaQueryWrapper<AlbumAttributeValue>().eq(AlbumAttributeValue::getAlbumId, albumId);
		List<AlbumAttributeValue> albumPropertyValueList = albumAttributeValueMapper.selectList(wrapper);
		albumInfo.setAlbumAttributeValueVoList(albumPropertyValueList);
		return albumInfo;
	}

	@Override
	public void updateAlbumInfo(Long albumId, AlbumInfoVo albumInfoVo) {
		AlbumInfo albumInfo = albumInfoMapper.selectById(albumId);
		if (albumInfo == null) {
			throw new GuiguException(201, "该专辑不存在");
		}
		BeanUtils.copyProperties(albumInfoVo, albumInfo); // 有的属性直接拷贝过去, vo没有的属性用老的albumInfo的属性值
		int i = albumInfoMapper.updateById(albumInfo);// 更新专辑基本信息
		log.info("更新专辑信息: {}", i > 0 ? "成功" : "失败");
		LambdaQueryWrapper<AlbumAttributeValue> albumAttributeValueLambdaQueryWrapper = new LambdaQueryWrapper<>();
		albumAttributeValueLambdaQueryWrapper.eq(AlbumAttributeValue::getAlbumId, albumId);
		int delete = albumAttributeValueMapper.delete(albumAttributeValueLambdaQueryWrapper);
		log.info("删除专辑的标签信息: {}", delete > 0 ? "成功" : "失败");
		//2. 保存专辑的标签信息(album_attribute_value表中插入数据)
		List<AlbumAttributeValue> attributeValues = albumInfoVo.getAlbumAttributeValueVoList().stream().map(albumAttributeValueVo -> {
			AlbumAttributeValue albumAttributeValue = new AlbumAttributeValue();
			albumAttributeValue.setAlbumId(albumId);
			albumAttributeValue.setAttributeId(albumAttributeValueVo.getAttributeId()); // 专辑的属性ID
			albumAttributeValue.setValueId(albumAttributeValueVo.getValueId());
			return albumAttributeValue;
		}).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(attributeValues)) {
			boolean b = albumAttributeValueService.saveBatch(attributeValues);
			log.info("保存专辑的标签信息: {}", b ? "成功" : "失败");
		}
		albumInfoMapper.updateById(albumInfo);
	}

	@Transactional
	public void saveAlbumStat(Long albumId) {

		ArrayList<String> albumStatus = new ArrayList<>();
		albumStatus.add(SystemConstant.ALBUM_STAT_PLAY);
		albumStatus.add(SystemConstant.ALBUM_STAT_BROWSE);
		albumStatus.add(SystemConstant.ALBUM_STAT_COMMENT);
		albumStatus.add(SystemConstant.ALBUM_STAT_SUBSCRIBE);

		for (String albumStatue : albumStatus) {
			AlbumStat albumStat = new AlbumStat();
			albumStat.setAlbumId(albumId);
			albumStat.setStatType(albumStatue);
			albumStat.setStatNum(0);
			albumStatMapper.insert(albumStat);
		}
	}
}
