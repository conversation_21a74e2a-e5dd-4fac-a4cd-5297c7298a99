package com.atguigu.tingshu.album.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @version 1.0
 */
public interface FileUploadService {

    /**
     * 图片文件上传
     * @param file
     * @return
     */
    String fileUploadService(MultipartFile file) throws IOException, NoSuchAlgorithmException, InvalidKeyException;
}
