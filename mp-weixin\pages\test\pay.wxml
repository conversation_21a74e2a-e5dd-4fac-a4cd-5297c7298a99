<view class="pay-result data-v-dbd3e5f3"><view class="pay-status data-v-dbd3e5f3"><t-icon wx:if="{{a}}" class="data-v-dbd3e5f3" u-i="dbd3e5f3-0" bind:__l="__l" u-p="{{a}}"/><text class="data-v-dbd3e5f3">订单支付测试</text></view><view class="data-v-dbd3e5f3" style="width:95%;font-size:28rpx;color:#333333;margin-top:5px;margin-bottom:10px"> 说明：<view class="data-v-dbd3e5f3"/> 微信小程序支付，需加入微信开发者，才可以测试支付；由于学员未加入开发者，故不能测试微信小程序支付；<view class="data-v-dbd3e5f3"/> 微信Native支付无限制，二者只是微信下单方式不一样，支付回调接口一致，因此未加入微信开发者人员，可在此测试微信支付。<view class="data-v-dbd3e5f3"/></view><view class="pay-order data-v-dbd3e5f3" style="width:95%"><uni-easyinput wx:if="{{c}}" class="data-v-dbd3e5f3" u-i="dbd3e5f3-1" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"/><button class="data-v-dbd3e5f3" type="text" style="margin-left:10px;font-size:14px" bindtap="{{d}}">生成支付</button></view><view class="qr-box data-v-dbd3e5f3"><canvas class="data-v-dbd3e5f3" canvas-id="qrcode" style="width:340rpx;height:340rpx;margin:0 auto"/></view><view class="btn-wrapper data-v-dbd3e5f3"><view class="status-btn data-v-dbd3e5f3" data-type="orderList" bindtap="{{e}}">订单详情</view><view class="status-btn data-v-dbd3e5f3" data-type="home" bindtap="{{f}}">订单列表</view></view></view>