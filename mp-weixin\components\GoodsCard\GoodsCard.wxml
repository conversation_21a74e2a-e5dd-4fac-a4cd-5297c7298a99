<view class="gui-margin-top gui-flex gui-row gui-space-between gui-m-l-20 gui-m-r-20 data-v-dd8935df"><view class="gui-bold gui-block gui-dark-bg-level-3 gui-color-drak gui-flex gui-row gui-align-items-center gui-justify-content-center data-v-dd8935df"><text class="gui-m-r-10 data-v-dd8935df">{{a}}</text></view><view bindtap="{{b}}" class="gui-dark-bg-level-3 gui-color-gray gui-flex data-v-dd8935df"><text class="gui-text-small data-v-dd8935df">查看全部</text><text class="gui-icons gui-block gui-color-drak data-v-dd8935df"></text></view></view><view class="gui-margin-top gui-padding-x gui-flex gui-row gui-wrap data-v-dd8935df"><view wx:for="{{c}}" wx:for-item="item" wx:key="f" class="gui-product data-v-dd8935df" hover-class="gui-tap" bindtap="{{item.g}}"><view class="gui-relative data-v-dd8935df"><text class="gui-absolute-lt gui-bg-red gui-p-l-5 gui-p-r-5 gui-text-small gui-color-white data-v-dd8935df">{{item.a}}</text><view class="gui-flex gui-absolute-lb gui-bg-black-opacity7 gui-p-l-5 gui-p-r-5 gui-text-small gui-color-white gui-p-t-5 gui-p-b-5 gui-p-l-20 gui-p-r-20 data-v-dd8935df"><text class="gui-icons gui-block gui-color-drak gui-m-r-5 gui-p-t-5 data-v-dd8935df"></text><text class="data-v-dd8935df">{{item.b}}</text></view><gui-image wx:if="{{item.d}}" class="data-v-dd8935df" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"></gui-image></view><view class="gui-product-lines data-v-dd8935df"><text class="gui-product-name gui-primary-text data-v-dd8935df">{{item.e}}</text></view><view class="data-v-dd8935df" style="height:30rpx"></view></view></view>