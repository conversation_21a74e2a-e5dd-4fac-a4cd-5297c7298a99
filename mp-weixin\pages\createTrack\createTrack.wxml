<gui-page class="data-v-47ea33ae" u-s="{{['gBody']}}" u-i="47ea33ae-0" bind:__l="__l"><view class="gui-padding gui-padding-x gui-bg-white data-v-47ea33ae" slot="gBody"><uni-forms wx:if="{{y}}" class="r data-v-47ea33ae" u-s="{{['d']}}" u-r="formDataRef" u-i="47ea33ae-1,47ea33ae-0" bind:__l="__l" bindupdateModelValue="{{x}}" u-p="{{y}}"><uni-forms-item wx:if="{{e}}" class="data-v-47ea33ae" u-s="{{['d']}}" u-i="47ea33ae-2,47ea33ae-1" bind:__l="__l" u-p="{{e}}"><cl-upload wx:if="{{d}}" class="{{['gui-flex', 'gui-space-between', 'data-v-47ea33ae', a]}}" bindonSuccess="{{b}}" u-i="47ea33ae-3,47ea33ae-2" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"></cl-upload></uni-forms-item><uni-forms-item wx:if="{{h}}" class="data-v-47ea33ae" u-s="{{['d']}}" u-i="47ea33ae-4,47ea33ae-1" bind:__l="__l" u-p="{{h}}"><uni-easyinput wx:if="{{g}}" class="data-v-47ea33ae" u-i="47ea33ae-5,47ea33ae-4" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"/></uni-forms-item><uni-forms-item wx:if="{{l}}" class="data-v-47ea33ae" u-s="{{['d']}}" u-i="47ea33ae-6,47ea33ae-1" bind:__l="__l" u-p="{{l}}"><cl-upload wx:if="{{k}}" class="gui-flex gui-space-between data-v-47ea33ae" bindonSuccess="{{i}}" u-i="47ea33ae-7,47ea33ae-6" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"></cl-upload></uni-forms-item><uni-forms-item wx:if="{{o}}" class="data-v-47ea33ae" u-s="{{['d']}}" u-i="47ea33ae-8,47ea33ae-1" bind:__l="__l" u-p="{{o}}"><uni-data-select wx:if="{{n}}" class="data-v-47ea33ae" u-i="47ea33ae-9,47ea33ae-8" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"></uni-data-select></uni-forms-item><uni-forms-item wx:if="{{r}}" class="data-v-47ea33ae" u-s="{{['d']}}" u-i="47ea33ae-10,47ea33ae-1" bind:__l="__l" u-p="{{r}}"><uni-easyinput wx:if="{{q}}" class="gui-textarea gui-bg-gray gui-dark-bg-level-1 data-v-47ea33ae" u-i="47ea33ae-11,47ea33ae-10" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"/></uni-forms-item><uni-forms-item wx:if="{{v}}" class="data-v-47ea33ae" u-s="{{['d']}}" u-i="47ea33ae-12,47ea33ae-1" bind:__l="__l" u-p="{{v}}"><switch class="data-v-47ea33ae" bindchange="{{s}}" checked="{{t}}" style="transform:scale(0.8)" color="#008AFF"/></uni-forms-item></uni-forms><button class="data-v-47ea33ae" type="primary" bindtap="{{z}}">提交</button><view class="data-v-47ea33ae" style="height:60rpx"></view></view></gui-page>