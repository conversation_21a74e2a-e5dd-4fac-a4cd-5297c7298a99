<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="70160987-9ff0-4f51-b142-e612f24f57a7" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="AnnotationType" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../../../../../develop/maven/apache-maven-3.5.0-1" />
        <option name="localRepository" value="D:\maven\apache-maven-3.6.3\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\develop\maven\apache-maven-3.5.0-1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="1.8" />
        <option name="workspaceImportForciblyTurnedOn" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="1.8" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2dWS0rAlAKe1kSYp4lRKPEtkc8s" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.ApiTest.byteApiTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ApiTest.minioApiTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ApiTest.testJwtApi.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ServerGatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ServiceAccountApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ServiceAlbumApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ServiceUserApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/哥-算法/大型微服务听书项目/part_one/day01_环境搭建/4_other/前置-资料/听书课件/后端初始化项目/tingshu-parent/service/service-album/src/main/resources&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;问题&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.5574713&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;9ba920ab1d5e39bedd0df5cf70a5c444&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.ServiceUserApplication.executor&quot;: &quot;Run&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql_aurora&quot;,
      &quot;mysql_aurora_aws&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\哥-算法\大型微服务听书项目\part_one\day01_环境搭建\4_other\前置-资料\听书课件\后端初始化项目\tingshu-parent\service\service-album\src\main\resources" />
      <recent name="D:\哥-算法\大型微服务听书项目\part_one\day01_环境搭建\4_other\前置-资料\听书课件\后端初始化项目\tingshu-parent\service\service-account\src\main\resources" />
      <recent name="D:\哥-算法\大型微服务听书项目\part_one\day01_环境搭建\4_other\前置-资料\听书课件\后端初始化项目\tingshu-parent\common\service-util\src\main\java\com\atguigu\tingshu\common\constant" />
      <recent name="D:\哥-算法\大型微服务听书项目\part_one\day01_环境搭建\4_other\前置-资料\听书课件\后端初始化项目\tingshu-parent\service\service-user\src\main\resources" />
      <recent name="D:\workspace\ideaworkspace\teach-code\project-code\tingshu_parent240829\service\service-user\src\main\resources" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.atguigu.tingshu.album.service.impl" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ServiceAlbumApplication">
    <configuration name="ServiceUserApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.atguigu.tingshu.ServiceUserApplication" />
      <module name="service-user" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.atguigu.tingshu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApiTest.byteApiTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="service-album" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.atguigu.tingshu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.atguigu.tingshu" />
      <option name="MAIN_CLASS_NAME" value="com.atguigu.tingshu.ApiTest" />
      <option name="METHOD_NAME" value="byteApiTest" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApiTest.minioApiTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="service-album" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.atguigu.tingshu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.atguigu.tingshu" />
      <option name="MAIN_CLASS_NAME" value="com.atguigu.tingshu.ApiTest" />
      <option name="METHOD_NAME" value="minioApiTest" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApiTest.testJwtApi" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="service-user" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.atguigu.user.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.atguigu.user" />
      <option name="MAIN_CLASS_NAME" value="com.atguigu.user.ApiTest" />
      <option name="METHOD_NAME" value="testJwtApi" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServerGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="server-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServerGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceAccountApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="service-account" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServiceAccountApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceAlbumApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="service-album" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServiceAlbumApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceDispatchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="service-dispatch" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServiceDispatchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceOrderApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="service-order" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServiceOrderApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServicePaymentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="service-payment" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServicePaymentApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceSearchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="service-search" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServiceSearchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ServiceUserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="service-user" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.atguigu.tingshu.ServiceUserApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.atguigu.tingshu.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.ApiTest.byteApiTest" />
      <item itemvalue="JUnit.ApiTest.minioApiTest" />
      <item itemvalue="JUnit.ApiTest.testJwtApi" />
      <item itemvalue="Spring Boot.ServiceAlbumApplication" />
      <item itemvalue="Spring Boot.ServiceAccountApplication" />
      <item itemvalue="Spring Boot.ServerGatewayApplication" />
      <item itemvalue="Spring Boot.ServiceDispatchApplication" />
      <item itemvalue="Spring Boot.ServiceOrderApplication" />
      <item itemvalue="Spring Boot.ServicePaymentApplication" />
      <item itemvalue="Spring Boot.ServiceSearchApplication" />
      <item itemvalue="Spring Boot.ServiceUserApplication" />
      <item itemvalue="应用程序.ServiceUserApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ServiceUserApplication" />
        <item itemvalue="JUnit.ApiTest.byteApiTest" />
        <item itemvalue="JUnit.ApiTest.minioApiTest" />
        <item itemvalue="JUnit.ApiTest.testJwtApi" />
        <item itemvalue="应用程序.ServiceUserApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-18abd8497189-intellij.indexing.shared.core-IU-241.14494.240" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-IU-241.14494.240" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SshConsoleOptionsProvider">
    <option name="myEncoding" value="UTF-8" />
  </component>
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="70160987-9ff0-4f51-b142-e612f24f57a7" name="Changes" comment="" />
      <created>1710122123204</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1710122123204</updated>
      <workItem from="1710122124475" duration="7673000" />
      <workItem from="1710293123075" duration="110000" />
      <workItem from="1729241927994" duration="410000" />
      <workItem from="1732886104221" duration="4249000" />
      <workItem from="1736494505063" duration="11550000" />
      <workItem from="1753674464546" duration="647000" />
      <workItem from="1753693455795" duration="1151000" />
      <workItem from="1753694631624" duration="177000" />
      <workItem from="1753694822266" duration="354000" />
      <workItem from="1753695191572" duration="1324000" />
      <workItem from="1753697012985" duration="6007000" />
      <workItem from="1753750800017" duration="2070000" />
      <workItem from="1753777833731" duration="983000" />
      <workItem from="1753778866818" duration="1496000" />
      <workItem from="1753780407474" duration="1357000" />
      <workItem from="1753782209120" duration="411000" />
      <workItem from="1753782641232" duration="199000" />
      <workItem from="1753782932423" duration="1693000" />
      <workItem from="1753838636053" duration="92000" />
      <workItem from="1753838744004" duration="79000" />
      <workItem from="1753838840443" duration="6769000" />
      <workItem from="1754102130508" duration="29877000" />
      <workItem from="1754273958977" duration="35458000" />
      <workItem from="1754399435365" duration="9873000" />
      <workItem from="1754473366488" duration="6000" />
      <workItem from="1754475771731" duration="25128000" />
      <workItem from="1754622799535" duration="7057000" />
      <workItem from="1754651460843" duration="6000" />
      <workItem from="1754707294732" duration="344000" />
      <workItem from="1754876362480" duration="22004000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="SHOW_DIRTY_RECURSIVELY" value="true" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>