package com.atguigu.user;

import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class ApiTest {

    @Test
    public void testJwtApi() {

//        //1. 得到jwtBuilder对象
//        JwtBuilder builder = Jwts.builder();
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("name", "hzk");
//        map.put("age", 18);
//        map.put("id", "1");
//
//        //2. 得到jsonWebToken对象()
//        String jwt = builder.setHeaderParam("alg", "HS256")
//                .setHeaderParam("typ", "JWT")
//                .setClaims(map)
//                .setIssuer("hzk")
//                .setIssuedAt(new Date())
//                .setExpiration(new Date(System.currentTimeMillis() + 1000 * 10))
//                .setSubject("测试使用")
//                .signWith(SignatureAlgorithm.HS256, "hzk" .getBytes())
//                .compact();
//
//        System.out.println("jwt = " + jwt);
//
//        jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiLmtYvor5Xkvb_nlKgiLCJuYW1lIjoiaHprIiwiaXNzIjoiaHprIiwiaWQiOiIxIiwiZXhwIjoxNzU0Mjc0MzQ0LCJpYXQiOjE3NTQyNzQzMzQsImFnZSI6MTh9.qvlC98qjW919KXUOa25Brge6vd0zkYu3kNyVf1rH5bk";
//
//        //载荷对象
//        Jws<Claims> claimsJws = Jwts.parser().setSigningKey("hzk".getBytes()).parseClaimsJws(jwt);
//
//        Object name = claimsJws.getBody().get("name");
//        System.out.println(name);

    }

}
