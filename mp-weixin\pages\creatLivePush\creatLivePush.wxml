<gui-page class="data-v-3038e123" u-s="{{['gBody']}}" u-i="3038e123-0" bind:__l="__l"><view class="gui-padding gui-padding-x gui-bg-white data-v-3038e123" slot="gBody"><uni-forms wx:if="{{p}}" class="r data-v-3038e123" u-s="{{['d']}}" u-r="formDataRef" u-i="3038e123-1,3038e123-0" bind:__l="__l" bindupdateModelValue="{{o}}" u-p="{{p}}"><uni-forms-item wx:if="{{c}}" class="data-v-3038e123" u-s="{{['d']}}" u-i="3038e123-2,3038e123-1" bind:__l="__l" u-p="{{c}}"><uni-easyinput wx:if="{{b}}" class="data-v-3038e123" u-i="3038e123-3,3038e123-2" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"/></uni-forms-item><uni-forms-item wx:if="{{g}}" class="data-v-3038e123" u-s="{{['d']}}" u-i="3038e123-4,3038e123-1" bind:__l="__l" u-p="{{g}}"><cl-upload wx:if="{{f}}" class="gui-flex gui-space-between data-v-3038e123" bindonSuccess="{{d}}" u-i="3038e123-5,3038e123-4" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"></cl-upload></uni-forms-item><uni-forms-item wx:if="{{j}}" class="data-v-3038e123" u-s="{{['d']}}" u-i="3038e123-6,3038e123-1" bind:__l="__l" u-p="{{j}}"><uni-data-select wx:if="{{i}}" class="data-v-3038e123" u-i="3038e123-7,3038e123-6" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"></uni-data-select></uni-forms-item><uni-forms-item wx:if="{{m}}" class="data-v-3038e123" u-s="{{['d']}}" u-i="3038e123-8,3038e123-1" bind:__l="__l" u-p="{{m}}"><uni-datetime-picker wx:if="{{l}}" class="data-v-3038e123" u-i="3038e123-9,3038e123-8" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"/></uni-forms-item></uni-forms><button class="data-v-3038e123" type="primary" bindtap="{{q}}">提交</button><view class="data-v-3038e123" style="height:60rpx"></view></view></gui-page>