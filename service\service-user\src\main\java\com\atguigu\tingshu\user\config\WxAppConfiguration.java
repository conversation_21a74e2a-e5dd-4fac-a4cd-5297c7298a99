package com.atguigu.tingshu.user.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.atguigu.tingshu.user.config.properties.WxAutoProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.jwt.crypto.sign.RsaSigner;
import org.springframework.security.rsa.crypto.KeyStoreKeyFactory;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.interfaces.RSAPrivateKey;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Configuration  // 微信的自动配置类
@EnableConfigurationProperties(WxAutoProperties.class)
public class WxAppConfiguration {

    @Autowired
    private WxAutoProperties wxAutoProperties;

    /**
     * 定义WxMaService的Bean对象
     */
    @Bean
    public WxMaService wxMaService() {
        //1. 创建WxMaServiceImpl对象
        WxMaService wxMaService = new WxMaServiceImpl();

        //2. 创建一个微信配置对象
        WxMaDefaultConfigImpl wxMaDefaultConfigImpl = new WxMaDefaultConfigImpl();
        wxMaDefaultConfigImpl.setAppid(wxAutoProperties.getAppId());
        wxMaDefaultConfigImpl.setSecret(wxAutoProperties.getAppSecret());

        //3. 将配置对象放入到WxMaService对象中
        wxMaService.setWxMaConfig(wxMaDefaultConfigImpl);
        return wxMaService;
    }

    //定义RSA签名的Bean对象
    @Bean
    public RsaSigner rsaSigner() {
        //1. 读取这个证书
        ClassPathResource classPathResource = new ClassPathResource("tingshu.jks");

        //2. 创建一个工厂对象
        KeyStoreKeyFactory keyStoreKeyFactory = new KeyStoreKeyFactory(classPathResource, "tingshu" .toCharArray());

        //3. 从工厂中获取钥匙对
        KeyPair keyPair = keyStoreKeyFactory.getKeyPair("tingshu", "tingshu" .toCharArray());

        //4. 获取私钥
        RSAPrivateKey aPrivate = (RSAPrivateKey)keyPair.getPrivate();

        RsaSigner rsaSigner = new RsaSigner(aPrivate);
        return rsaSigner;
    }
}
