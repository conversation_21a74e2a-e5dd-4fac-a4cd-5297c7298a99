<view class="uni-calendar" bindmouseleave="{{P}}"><view wx:if="{{a}}" class="{{['uni-calendar__mask', b && 'uni-calendar--mask-show']}}" bindtap="{{c}}"></view><view wx:if="{{d}}" class="{{['uni-calendar__content', M && 'uni-calendar--fixed', N && 'uni-calendar--ani-show', O && 'uni-calendar__content-mobile']}}"><view class="{{['uni-calendar__header', l && 'uni-calendar__header-mobile']}}"><view class="uni-calendar__header-btn-box" catchtap="{{e}}"><view class="uni-calendar__header-btn uni-calendar--left"></view></view><picker mode="date" value="{{g}}" fields="month" bindchange="{{h}}"><text class="uni-calendar__header-text">{{f}}</text></picker><view class="uni-calendar__header-btn-box" catchtap="{{i}}"><view class="uni-calendar__header-btn uni-calendar--right"></view></view><view wx:if="{{j}}" class="dialog-close" bindtap="{{k}}"><view class="dialog-close-plus" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate" data-id="close"></view></view></view><view class="uni-calendar__box"><view wx:if="{{m}}" class="uni-calendar__box-bg"><text class="uni-calendar__box-bg-text">{{n}}</text></view><view class="uni-calendar__weeks" style="padding-bottom:7px"><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{o}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{p}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{q}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{r}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{s}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{t}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{v}}</text></view></view><view wx:for="{{w}}" wx:for-item="item" wx:key="b" class="uni-calendar__weeks"><view wx:for="{{item.a}}" wx:for-item="weeks" wx:key="e" class="uni-calendar__weeks-item"><calendar-item wx:if="{{weeks.d}}" class="uni-calendar-item--hook" bindchange="{{weeks.a}}" bindhandleMouse="{{weeks.b}}" u-i="{{weeks.c}}" bind:__l="__l" u-p="{{weeks.d}}"></calendar-item></view></view></view><view wx:if="{{x}}" class="uni-date-changed uni-calendar--fixed-top" style="padding:0 80px"><view class="uni-date-changed--time-date">{{y}}</view><time-picker wx:if="{{A}}" class="time-picker-style" u-i="c9c5c7a6-1" bind:__l="__l" bindupdateModelValue="{{z}}" u-p="{{A}}"></time-picker></view><view wx:if="{{B}}" class="uni-date-changed uni-calendar--fixed-top"><view class="uni-date-changed--time-start"><view class="uni-date-changed--time-date">{{C}}</view><time-picker wx:if="{{E}}" class="time-picker-style" u-i="c9c5c7a6-2" bind:__l="__l" bindupdateModelValue="{{D}}" u-p="{{E}}"></time-picker></view><view style="line-height:50px"><uni-icons wx:if="{{F}}" u-i="c9c5c7a6-3" bind:__l="__l" u-p="{{F}}"></uni-icons></view><view class="uni-date-changed--time-end"><view class="uni-date-changed--time-date">{{G}}</view><time-picker wx:if="{{I}}" class="time-picker-style" u-i="c9c5c7a6-4" bind:__l="__l" bindupdateModelValue="{{H}}" u-p="{{I}}"></time-picker></view></view><view wx:if="{{J}}" class="uni-date-changed uni-date-btn--ok"><view class="uni-datetime-picker--btn" bindtap="{{L}}">{{K}}</view></view></view></view>