<view class="uni-section"><view class="uni-section-header" bindtap="{{k}}"><view wx:if="{{a}}" class="{{['uni-section-header__decoration', b]}}"/><slot wx:else name="decoration"></slot><view class="uni-section-header__content"><text style="{{'font-size:' + d + ';' + ('color:' + e)}}" class="{{['uni-section__content-title', f && 'distraction']}}">{{c}}</text><text wx:if="{{g}}" style="{{'font-size:' + i + ';' + ('color:' + j)}}" class="uni-section-header__content-sub">{{h}}</text></view><view class="uni-section-header__slot-right"><slot name="right"></slot></view></view><view class="uni-section-content" style="{{'padding:' + l}}"><slot/></view></view>