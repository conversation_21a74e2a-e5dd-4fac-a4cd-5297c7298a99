<gui-page class="data-v-f2ac770a" u-s="{{['gBody']}}" u-i="f2ac770a-0" bind:__l="__l"><view slot="gBody"><view class="gui-padding gui-padding-x gui-bg-white data-v-f2ac770a"><uni-forms wx:if="{{S}}" class="r data-v-f2ac770a" u-s="{{['d']}}" u-r="formDataRef" u-i="f2ac770a-1,f2ac770a-0" bind:__l="__l" bindupdateModelValue="{{R}}" u-p="{{S}}"><uni-forms-item wx:if="{{c}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-2,f2ac770a-1" bind:__l="__l" u-p="{{c}}"><uni-easyinput wx:if="{{b}}" class="data-v-f2ac770a" u-i="f2ac770a-3,f2ac770a-2" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"/></uni-forms-item><uni-forms-item wx:if="{{g}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-4,f2ac770a-1" bind:__l="__l" u-p="{{g}}"><cl-upload wx:if="{{f}}" class="gui-flex gui-space-between data-v-f2ac770a" bindonSuccess="{{d}}" u-i="f2ac770a-5,f2ac770a-4" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"></cl-upload></uni-forms-item><uni-forms-item wx:if="{{k}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-6,f2ac770a-1" bind:__l="__l" u-p="{{k}}"><uni-data-picker wx:if="{{j}}" u-r="categoryPopupPickerRef" class="gui-flex1 r data-v-f2ac770a" u-i="f2ac770a-7,f2ac770a-6" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"></uni-data-picker></uni-forms-item><uni-forms-item wx:if="{{l}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-8,f2ac770a-1" bind:__l="__l" u-p="{{p}}"><view class="gui-color-grey2 attr-tags-container data-v-f2ac770a" bindtap="{{o}}"><view wx:if="{{m}}" class="click-tags-text data-v-f2ac770a"><text class="data-v-f2ac770a">点击选择标签</text></view><view wx:else class="gui-flex gui-wrap gui-m-t-5 gui-m-b-5 data-v-f2ac770a"><uni-tag wx:for="{{n}}" wx:for-item="item" wx:key="a" class="gui-m-r-10 gui-m-t-10 gui-m-b-10 data-v-f2ac770a" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"></uni-tag></view></view></uni-forms-item><uni-forms-item wx:if="{{s}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-10,f2ac770a-1" bind:__l="__l" u-p="{{s}}"><uni-easyinput wx:if="{{r}}" class="gui-textarea gui-bg-gray gui-dark-bg-level-1 data-v-f2ac770a" u-i="f2ac770a-11,f2ac770a-10" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"/></uni-forms-item><uni-forms-item wx:if="{{w}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-12,f2ac770a-1" bind:__l="__l" u-p="{{w}}"><switch class="data-v-f2ac770a" bindchange="{{t}}" checked="{{v}}" style="transform:scale(0.8)" color="#008AFF"/></uni-forms-item><uni-forms-item wx:if="{{z}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-13,f2ac770a-1" bind:__l="__l" u-p="{{z}}"><radio-group bindchange="{{y}}" class="payment-type-radio data-v-f2ac770a"><label wx:for="{{x}}" wx:for-item="item" wx:key="d" class="payment-type-radio-label data-v-f2ac770a"><view class="payment-type-radio-label-radio data-v-f2ac770a" style="transform:scale(0.8)"><radio class="data-v-f2ac770a" value="{{item.a}}" checked="{{item.b}}"/></view><view class="gui-text-small gui-color-gray data-v-f2ac770a">{{item.c}}</view></label></radio-group></uni-forms-item><view class="data-v-f2ac770a" hidden="{{!P}}"><uni-forms wx:if="{{O}}" class="r data-v-f2ac770a" u-s="{{['d']}}" u-r="payTypeFormDataRef" u-i="f2ac770a-14,f2ac770a-1" bind:__l="__l" bindupdateModelValue="{{N}}" u-p="{{O}}"><uni-forms-item wx:if="{{C}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-15,f2ac770a-14" bind:__l="__l" u-p="{{C}}"><radio-group bindchange="{{B}}" class="payment-type-radio data-v-f2ac770a"><label wx:for="{{A}}" wx:for-item="item" wx:key="d" class="payment-type-radio-label data-v-f2ac770a"><view class="payment-type-radio-label-radio data-v-f2ac770a" style="transform:scale(0.8)"><radio class="data-v-f2ac770a" value="{{item.a}}" checked="{{item.b}}"/></view><view class="gui-text-small gui-color-gray data-v-f2ac770a">{{item.c}}</view></label></radio-group></uni-forms-item><uni-forms-item wx:if="{{F}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-16,f2ac770a-14" bind:__l="__l" u-p="{{F}}"><uni-easyinput wx:if="{{E}}" class="data-v-f2ac770a" u-i="f2ac770a-17,f2ac770a-16" bind:__l="__l" bindupdateModelValue="{{D}}" u-p="{{E}}"/></uni-forms-item><uni-forms-item wx:if="{{I}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-18,f2ac770a-14" bind:__l="__l" u-p="{{I}}"><uni-easyinput wx:if="{{H}}" class="data-v-f2ac770a" u-i="f2ac770a-19,f2ac770a-18" bind:__l="__l" bindupdateModelValue="{{G}}" u-p="{{H}}"/></uni-forms-item><uni-forms-item wx:if="{{L}}" class="data-v-f2ac770a" u-s="{{['d']}}" u-i="f2ac770a-20,f2ac770a-14" bind:__l="__l" u-p="{{L}}"><uni-easyinput wx:if="{{K}}" class="data-v-f2ac770a" u-i="f2ac770a-21,f2ac770a-20" bind:__l="__l" bindupdateModelValue="{{J}}" u-p="{{K}}"/></uni-forms-item></uni-forms></view></uni-forms><button class="data-v-f2ac770a" type="primary" bindtap="{{T}}">提交</button><view class="data-v-f2ac770a" style="height:60rpx"></view></view><attribute-popup wx:if="{{V}}" class="r data-v-f2ac770a" u-r="attributePopupRef" u-i="f2ac770a-22,f2ac770a-0" bind:__l="__l" u-p="{{V}}"></attribute-popup></view></gui-page>