<gui-page class="data-v-e4e4508d" u-s="{{['gBody']}}" u-i="e4e4508d-0" bind:__l="__l"><view class="data-v-e4e4508d" style="padding:50rpx" slot="gBody"><view class="gui-flex gui-row gui-justify-content-center data-v-e4e4508d"><gui-image wx:if="{{a}}" class="data-v-e4e4508d" u-i="e4e4508d-1,e4e4508d-0" bind:__l="__l" u-p="{{a}}"></gui-image></view><view class="data-v-e4e4508d" style="margin-top:80rpx"><form class="data-v-e4e4508d" bindsubmit="{{c}}"><view class="data-v-e4e4508d"><text class="gui-text-small gui-color-gray data-v-e4e4508d">账户</text></view><view class="gui-border-b data-v-e4e4508d"><input type="text" class="gui-form-input data-v-e4e4508d" name="username" placeholder="登录账户"/></view><view class="gui-margin-top data-v-e4e4508d"><text class="gui-text-small gui-color-gray data-v-e4e4508d">密码</text></view><view class="gui-border-b data-v-e4e4508d"><input type="password" class="gui-form-input data-v-e4e4508d" name="password" placeholder="密码"/></view><view class="gui-margin-top gui-flex gui-rows gui-space-between data-v-e4e4508d" hover-class="gui-tap"><text class="gui-text gui-color-gray gui-block gui-text-right data-v-e4e4508d" bindtap="{{b}}">短信登录</text></view><view class="data-v-e4e4508d" style="margin-top:38rpx"><button type="default" class="gui-button gui-bg-primary gui-noborder data-v-e4e4508d" formType="submit" style="border-radius:50rpx"><text class="gui-color-white gui-button-text data-v-e4e4508d">登 录</text></button></view></form></view><view class="gui-flex gui-rows gui-nowrap gui-align-items-center data-v-e4e4508d" style="margin-top:80rpx"><view class="gui-title-line data-v-e4e4508d"></view><text class="gui-primary-color gui-h6 data-v-e4e4508d" style="padding-left:50rpx;padding-right:50rpx">其他方式登录</text><view class="gui-title-line data-v-e4e4508d"></view></view><view class="gui-flex gui-rows gui-nowrap gui-justify-content-center gui-margin-top data-v-e4e4508d"><view bindtap="{{d}}" class="other-login-icons data-v-e4e4508d" hover-class="gui-tap"><text class="other-login-icons gui-icons gui-color-gray data-v-e4e4508d"></text></view><view class="other-login-icons data-v-e4e4508d" hover-class="gui-tap"><text class="other-login-icons gui-icons gui-color-gray data-v-e4e4508d"></text></view></view></view></gui-page><uni-popup wx:if="{{h}}" class="r data-v-e4e4508d" u-s="{{['d']}}" u-r="wxLoginDialogRef" u-i="e4e4508d-2" bind:__l="__l" u-p="{{h}}"><uni-popup-dialog wx:if="{{f}}" class="data-v-e4e4508d" bindconfirm="{{e}}" u-i="e4e4508d-3,e4e4508d-2" bind:__l="__l" u-p="{{f}}"></uni-popup-dialog></uni-popup>