<gui-popup wx:if="{{d}}" class="r data-v-486ead6e" u-s="{{['d']}}" u-r="popup" u-i="486ead6e-0" bind:__l="__l" u-p="{{d}}"><view class="gui-relative pop-top-container data-v-486ead6e"><view class="gui-flex gui-row gui-space-between data-v-486ead6e"><view class="gui-p-t-20 gui-p-b-20 gui-color-white gui-m-l-40 data-v-486ead6e">请选择专辑标签</view><view class="gui-icons gui-color-white gui-p-t-20 gui-p-b-20 gui-m-r-40 data-v-486ead6e" bindtap="{{a}}"></view></view></view><view class="gui-bg-white gui-dark-bg-level-3 gui-padding data-v-486ead6e"><scroll-view class="data-v-486ead6e" show-scrollbar="{{false}}" scroll-y="{{true}}" style="height:720rpx"><view wx:for="{{b}}" wx:for-item="item" wx:key="f" class="gui-scroll-y-items item-container data-v-486ead6e"><text class="item-label data-v-486ead6e">{{item.a}}</text><view class="item-body data-v-486ead6e"><uni-data-checkbox wx:if="{{item.e}}" class="data-v-486ead6e" bindchange="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" bindupdateModelValue="{{item.d}}" u-p="{{item.e}}"></uni-data-checkbox></view></view></scroll-view></view></gui-popup>