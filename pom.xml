<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.0.4</version>
    </parent>



    <groupId>com.atguigu.tingshu</groupId>
    <artifactId>tingshu_parent241028</artifactId>
    <packaging>pom</packaging>
    <version>1.0</version>

    <modules>
        <module>common</module>
        <module>model</module>
        <module>service</module>
        <module>server-gateway</module>
        <module>service-client</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <cloud.version>2022.0.2</cloud.version>
        <alibaba.version>2022.0.0.0-RC2</alibaba.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <mysql.version>8.0.30</mysql.version>
        <knife4j.version>4.1.0</knife4j.version>
        <fastjson.version>1.2.29</fastjson.version>
        <vod_api.version>2.1.4</vod_api.version>
        <minio.version>8.2.0</minio.version>
        <jodatime.version>2.10.1</jodatime.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <wxpay.version>0.2.9</wxpay.version>
        <redisson.version>3.20.0</redisson.version>
        <guava.version>23.0</guava.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <commons-io.version>2.11.0</commons-io.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <!--配置dependencyManagement锁定依赖的版本-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--mybatis-plus 持久层-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!--knife4j https://doc.xiaominfo.com/docs/quick-start-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 腾讯云VOD -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>vod_api</artifactId>
                <version>${vod_api.version}</version>
            </dependency>

            <!-- minio -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!--日期时间工具-->
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${jodatime.version}</version>
            </dependency>

            <!-- xxl-job -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.wechatpay-apiv3</groupId>
                <artifactId>wechatpay-java</artifactId>
                <version>${wxpay.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- list集合拆分 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 下载：https://pinyin4j.sourceforge.net/ -->
            <!-- 安装：mvn install:install-file "-Dfile=D:\work\tingshu_work\tools\pinyin4j-2.5.0\lib\pinyin4j-2.5.0.jar" "-DgroupId=com.belerweb" "-DartifactId=pinyin4j" "-Dversion=2.5.0" "-Dpackaging=jar" -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>


            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>