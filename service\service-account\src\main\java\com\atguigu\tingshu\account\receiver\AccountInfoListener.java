package com.atguigu.tingshu.account.receiver;

import com.atguigu.tingshu.account.service.MyOpsService;
import com.atguigu.tingshu.common.execption.GuiguException;
import com.atguigu.tingshu.common.rabbit.constant.MqConst;
import com.atguigu.tingshu.common.util.MD5;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import io.micrometer.common.util.StringUtils;
import io.netty.util.internal.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 */

@Component
@Slf4j
public class AccountInfoListener {


    @Autowired
    private MyOpsService  myOpsService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConst.QUEUE_USER_REGISTER, durable = "true"), exchange = @Exchange(value = MqConst.EXCHANGE_USER, durable = "true"), key = MqConst.ROUTING_USER_REGISTER))
    @SneakyThrows // SneakyThrows可以绕开编译时候的异常 但是真正运行期间出现异常依然会抛出来
    public void listenUserAccountRegister(String content, Message message, Channel channel) {

        //1. 判断消息是否存在
        if (StringUtils.isEmpty(content)) {
            return; // 不用消费
        }

        //2. 处理消息(TODO)

        //3. 消费消息
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            myOpsService.userAccountRegister(content);
            channel.basicAck(deliveryTag, false);
        } catch (GuiguException e) {
            String msgMd5 = MD5.encrypt(content);
            String msgRetryKey = "msg:retry:" + msgMd5;
            Long count = redisTemplate.opsForValue().increment(msgRetryKey);
            //三次重试
            if (count >= 3) {
                log.error("消息已经重试了{}次， 到达了重试的次数, 请人工排查错误原因: {}", count, e.getMessage());
                //不能重试
                channel.basicNack(deliveryTag, false, false);
                redisTemplate.delete(msgRetryKey); // 删除重试次数的key
            } else {
                //能重试
                log.info("消息重试{}次", count);
                channel.basicNack(deliveryTag, false, true);
            }
        } catch (Exception e) {
            log.info("签收消息时网络出现了故障, 异常原因, {}", e.getMessage());
            channel.basicNack(deliveryTag, false, false);
        }

        //4. 手动应答消息(从队列中删除掉)
        try {
            channel.basicAck(deliveryTag, false);
        } catch (IOException e) {
            log.error("网络故障导致签收消息失败{}", e.getMessage());
        }

    }
}
