package com.atguigu.tingshu;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.UploadObjectArgs;
import io.minio.errors.MinioException;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class ApiTest {

    @Test
    public void minioApiTest() throws IOException, NoSuchAlgorithmException, InvalidKeyException {


        try {
            //1. 创建 minio客户端
            MinioClient minioClient =
                    MinioClient.builder()
                            .endpoint("http://192.168.200.110:9000")
                            .credentials("admin", "admin123456")
                            .build();

            //2. 判断桶是否存在
            boolean found =
                    minioClient.bucketExists(BucketExistsArgs.builder().bucket("demo").build());
            if (!found) {
                //3. 创建桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket("demo").build());
            } else {
                System.out.println("桶已经存在");
            }

            //4. 上传文件到桶中
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket("demo")
                            .object("muscle.png")
                            .filename("D:/testMinio/muscle.png")
                            .build());
            System.out.println("上传成功");
        } catch (MinioException e) {
            System.out.println("Error occurred: " + e.getMessage());
        }
    }
}
